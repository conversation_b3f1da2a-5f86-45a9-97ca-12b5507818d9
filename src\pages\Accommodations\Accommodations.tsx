import React, { useState } from 'react';
import BackgroundEffects from '../../components/BackgroundEffects/BackgroundEffects';

interface Accommodation {
  id: number;
  name: string;
  type: string;
  capacity: number;
  price: string;
  image: string;
  amenities: string[];
  description: string;
  features: string[];
}

const accommodations: Accommodation[] = [
  {
    id: 1,
    name: 'Cedar Bungalow',
    type: 'Bungalow',
    capacity: 4,
    price: '$120/night',
    image: '/Assets/TentWHammock.jpg',
    amenities: ['Fireplace', 'Full Kitchen', 'WiFi', 'Private Bathroom', 'Deck'],
    description: 'Cozy cedar bungalow perfect for families seeking comfort in nature.',
    features: ['2 Bedrooms', 'Living Area', 'Outdoor Fire Pit', 'Forest Views']
  },
  {
    id: 2,
    name: 'Pine Tentalow',
    type: 'Glamping',
    capacity: 2,
    price: '$85/night',
    image: '/Assets/InsideTentPOV.jpg',
    amenities: ['Heating', 'Electricity', 'Shared Bathroom', 'Outdoor Seating'],
    description: 'Luxury camping experience with modern amenities and stunning views.',
    features: ['Queen Bed', 'Skylight', 'Private Deck', 'Mountain Views']
  },
  {
    id: 3,
    name: 'Forest Cabin',
    type: 'Cabin',
    capacity: 6,
    price: '$150/night',
    image: '/Assets/PersonOnHammock.jpg',
    amenities: ['Full Kitchen', 'WiFi', '2 Bathrooms', 'Hot Tub', 'BBQ Grill'],
    description: 'Spacious cabin ideal for larger groups and extended stays.',
    features: ['3 Bedrooms', 'Large Deck', 'Game Room', 'Lakeside Location']
  },
  {
    id: 4,
    name: 'Riverside Tent',
    type: 'Camping',
    capacity: 2,
    price: '$45/night',
    image: '/Assets/CampingWFriends.jpg',
    amenities: ['Shared Facilities', 'Fire Ring', 'Picnic Table', 'Water Access'],
    description: 'Traditional camping experience by the peaceful riverside.',
    features: ['Tent Site', 'River Access', 'Hiking Trails', 'Stargazing']
  }
];

const Accommodations: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filters = ['All', 'Bungalow', 'Glamping', 'Cabin', 'Camping'];

  const filteredAccommodations = selectedFilter === 'All'
    ? accommodations
    : accommodations.filter(acc => acc.type === selectedFilter);

  return (
    <div className="min-h-screen py-16 bg-warm-beige relative">
      <BackgroundEffects variant="forest" />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-nature font-bold text-forest-green mb-4">
            Accommodations
          </h1>
          <p className="text-xl text-stone-gray max-w-3xl mx-auto">
            Choose from our range of comfortable accommodations, each designed to enhance your connection with nature.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {filters.map((filter) => (
            <button
              key={filter}
              onClick={() => setSelectedFilter(filter)}
              className={`px-6 py-2 rounded-full font-medium transition-colors duration-300 ${
                selectedFilter === filter
                  ? 'bg-forest-green text-white'
                  : 'bg-white text-stone-gray hover:bg-sage-green hover:text-white'
              }`}
            >
              {filter}
            </button>
          ))}
        </div>

        {/* Accommodations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredAccommodations.map((accommodation) => (
            <div
              key={accommodation.id}
              className="card hover:shadow-xl transition-shadow duration-300 cursor-pointer"
              onClick={() => alert(`${accommodation.name} selected! Booking system coming soon. Please contact us directly.`)}
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={accommodation.image}
                  alt={accommodation.name}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-forest-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {accommodation.price}
                </div>
              </div>

              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-xl font-semibold text-forest-green">{accommodation.name}</h3>
                  <span className="text-sm text-stone-gray bg-sage-green bg-opacity-20 px-2 py-1 rounded">
                    {accommodation.type}
                  </span>
                </div>

                <p className="text-stone-gray text-sm mb-4">{accommodation.description}</p>

                <div className="flex items-center justify-between text-sm text-stone-gray mb-4">
                  <span>👥 Up to {accommodation.capacity} guests</span>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {accommodation.amenities.slice(0, 3).map((amenity, index) => (
                    <span
                      key={index}
                      className="text-xs bg-moss-green bg-opacity-20 text-forest-green px-2 py-1 rounded"
                    >
                      {amenity}
                    </span>
                  ))}
                  {accommodation.amenities.length > 3 && (
                    <span className="text-xs text-stone-gray">
                      +{accommodation.amenities.length - 3} more
                    </span>
                  )}
                </div>

                <button className="w-full btn-primary text-sm">
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Accommodations;
