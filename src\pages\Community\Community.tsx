import React, { useState, useRef } from 'react';
import BackgroundEffects from '../../components/BackgroundEffects/BackgroundEffects';

interface Story {
  id: number;
  title: string;
  author: string;
  date: string;
  content: string;
  image: string;
  tags: string[];
  likes: number;
}

interface StoryFormData {
  title: string;
  author: string;
  content: string;
  tags: string;
  photo: File | null;
}

interface StorySubmission {
  id: string;
  title: string;
  author: string;
  content: string;
  tags: string[];
  photo: File | null;
  photoUrl?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
}

// Sample community stories for MVP
const sampleStories: Story[] = [
  {
    id: 1,
    title: 'My First Solo Camping Adventure',
    author: '<PERSON>',
    date: '2024-12-15',
    content: 'I never thought I could camp alone, but Snawbra Campsite made it feel so safe and welcoming. The guided nature trail helped me connect with other solo travelers, and the AI concierge was incredibly helpful in planning my perfect getaway. The Pine Tentalow was the perfect balance of comfort and outdoor experience. I watched the sunrise from my private deck and felt more connected to nature than ever before.',
    image: '/Assets/PersonOnHammock.jpg',
    tags: ['Solo Travel', 'First Time', 'Nature Connection'],
    likes: 24
  },
  {
    id: 2,
    title: 'Family Memories at Cedar Bungalow',
    author: '<PERSON> & <PERSON> T.',
    date: '2024-12-10',
    content: 'Our kids (ages 8 and 12) had the most amazing time! The Cedar Bungalow was spacious enough for our family of four, and the kids loved the guided horseback riding. The campfire stories session was a highlight - our youngest is still talking about the local legends. The family game night brought us all together without any screens. We\'re already planning our next visit!',
    image: '/Assets/CampingWFriends.jpg',
    tags: ['Family', 'Kids', 'Horseback Riding'],
    likes: 31
  },
  {
    id: 3,
    title: 'Photography Workshop Changed My Perspective',
    author: 'David L.',
    date: '2024-12-05',
    content: 'As an amateur photographer, I was blown away by the quality of instruction in the Forest Photography Workshop. Learning macro techniques with the pine close-ups and capturing the golden hour light filtering through the trees was magical. The instructor was patient and knowledgeable, and I left with not just better photos, but a deeper appreciation for the small details in nature.',
    image: '/Assets/PineCloseUp.jpg',
    tags: ['Photography', 'Workshop', 'Learning'],
    likes: 18
  },
  {
    id: 4,
    title: 'Romantic Getaway Perfection',
    author: 'Emma & James',
    date: '2024-11-28',
    content: 'We came for our anniversary and couldn\'t have asked for a more perfect romantic retreat. The Pine Tentalow with forest views was incredibly intimate, and the stargazing session was unforgettable. The meditation and mindfulness experience helped us reconnect not just with nature, but with each other. The AI concierge even suggested the perfect hiking trail for a sunrise proposal - she said yes!',
    image: '/Assets/TentWHammock.jpg',
    tags: ['Romance', 'Anniversary', 'Proposal'],
    likes: 42
  },
  {
    id: 5,
    title: 'Wilderness Skills That Actually Matter',
    author: 'Alex R.',
    date: '2024-11-20',
    content: 'The Wilderness Survival Skills workshop was intense but incredibly rewarding. Learning fire-making techniques, shelter building, and navigation skills in a safe environment gave me confidence for future backcountry adventures. The instructor was a true expert, and the hands-on approach made everything stick. I feel much more prepared for my upcoming solo hiking trip.',
    image: '/Assets/HikeInForest.jpg',
    tags: ['Survival Skills', 'Adventure', 'Confidence Building'],
    likes: 27
  }
];

const Community: React.FC = () => {
  const [stories, setStories] = useState<Story[]>(sampleStories);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<StoryFormData>({
    title: '',
    author: '',
    content: '',
    tags: '',
    photo: null
  });
  const [selectedTag, setSelectedTag] = useState('All');
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const formRef = useRef<HTMLDivElement>(null);

  // Get all unique tags
  const allTags = ['All', ...Array.from(new Set(stories.flatMap(story => story.tags)))];

  // Filter stories by selected tag
  const filteredStories = selectedTag === 'All'
    ? stories
    : stories.filter(story => story.tags.includes(selectedTag));

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({ ...prev, photo: file }));

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const scrollToForm = () => {
    setShowForm(true);
    setTimeout(() => {
      formRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }, 100);
  };

  const handleSubmitStory = (e: React.FormEvent) => {
    e.preventDefault();

    // Create submission object for admin review
    const submission: StorySubmission = {
      id: `submission_${Date.now()}`,
      title: formData.title,
      author: formData.author,
      content: formData.content,
      tags: formData.tags.split(',').map(tag => tag.trim()),
      photo: formData.photo,
      photoUrl: photoPreview || undefined,
      status: 'pending',
      submittedAt: new Date()
    };

    // Store in localStorage for admin dashboard (in real app, this would be sent to backend)
    const existingSubmissions = JSON.parse(localStorage.getItem('storySubmissions') || '[]');
    existingSubmissions.push(submission);
    localStorage.setItem('storySubmissions', JSON.stringify(existingSubmissions));

    // Log to console for MVP
    console.log('Story submitted for admin review:', submission);

    // Reset form
    setFormData({ title: '', author: '', content: '', tags: '', photo: null });
    setPhotoPreview(null);
    setShowForm(false);

    // Show success message
    alert('Thank you for sharing your story! It has been submitted for review and will appear on our community page once approved.');
  };

  const handleLike = (storyId: number) => {
    setStories(prev => prev.map(story =>
      story.id === storyId
        ? { ...story, likes: story.likes + 1 }
        : story
    ));
  };

  return (
    <div className="min-h-screen py-16 bg-warm-beige relative">
      <BackgroundEffects variant="sunset" />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-nature font-bold text-forest-green mb-4">
            Community Stories
          </h1>
          <p className="text-xl text-stone-gray max-w-3xl mx-auto">
            Read inspiring stories from fellow adventurers and share your own experiences.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center mb-8">
          <button
            onClick={scrollToForm}
            className="relative overflow-hidden group bg-gradient-to-r from-forest-green to-pine-green text-white px-8 py-4 rounded-full font-bold text-lg transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
          >
            <span className="relative z-10 flex items-center space-x-3">
              <span>📸 Share Your Adventure</span>
              <svg className="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </span>
            <div className="absolute inset-0 bg-gradient-to-r from-pine-green to-sage-green opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>

        {/* Story Submission Form */}
        {showForm && (
          <div ref={formRef} className="max-w-2xl mx-auto mb-12 bg-white rounded-lg shadow-xl p-8 border-2 border-forest-green">
            <div className="text-center mb-6">
              <h2 className="text-3xl font-nature font-bold text-forest-green mb-2">📸 Share Your Adventure</h2>
              <p className="text-stone-gray">Tell us about your amazing experience and upload a photo to make it memorable!</p>
            </div>
            <form onSubmit={handleSubmitStory} className="space-y-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-stone-gray mb-2">
                  Story Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="Give your story a catchy title..."
                />
              </div>

              <div>
                <label htmlFor="author" className="block text-sm font-medium text-stone-gray mb-2">
                  Your Name *
                </label>
                <input
                  type="text"
                  id="author"
                  name="author"
                  value={formData.author}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="How should we credit you?"
                />
              </div>

              <div>
                <label htmlFor="content" className="block text-sm font-medium text-stone-gray mb-2">
                  Your Story *
                </label>
                <textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="Tell us about your experience at Snawbra Campsite..."
                />
              </div>

              <div>
                <label htmlFor="tags" className="block text-sm font-medium text-stone-gray mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  id="tags"
                  name="tags"
                  value={formData.tags}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="e.g., Family, Adventure, Photography"
                />
              </div>

              {/* Photo Upload */}
              <div>
                <label className="block text-sm font-medium text-stone-gray mb-2">
                  📸 Share a Photo from Your Adventure *
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-forest-green transition-colors">
                  {photoPreview ? (
                    <div className="space-y-4">
                      <img
                        src={photoPreview}
                        alt="Preview"
                        className="max-h-48 mx-auto rounded-lg shadow-md"
                      />
                      <div className="flex justify-center space-x-4">
                        <label className="cursor-pointer bg-forest-green text-white px-4 py-2 rounded-lg hover:bg-pine-green transition-colors">
                          Change Photo
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handlePhotoChange}
                            className="hidden"
                            required
                          />
                        </label>
                        <button
                          type="button"
                          onClick={() => {
                            setPhotoPreview(null);
                            setFormData(prev => ({ ...prev, photo: null }));
                          }}
                          className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                      <label className="cursor-pointer">
                        <span className="text-forest-green font-semibold hover:text-pine-green">Upload a photo</span>
                        <span className="text-gray-500"> or drag and drop</span>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handlePhotoChange}
                          className="hidden"
                          required
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-2">PNG, JPG, GIF up to 10MB</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setPhotoPreview(null);
                    setFormData({ title: '', author: '', content: '', tags: '', photo: null });
                  }}
                  className="flex-1 bg-gray-500 text-white py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button type="submit" className="flex-1 bg-gradient-to-r from-forest-green to-pine-green text-white py-3 rounded-lg font-bold hover:from-pine-green hover:to-sage-green transition-all duration-300 transform hover:scale-105">
                  📸 Submit for Review
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Tag Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          {allTags.map((tag) => (
            <button
              key={tag}
              onClick={() => setSelectedTag(tag)}
              className={`px-4 py-2 rounded-full font-medium transition-colors duration-300 ${
                selectedTag === tag
                  ? 'bg-forest-green text-white'
                  : 'bg-white text-stone-gray hover:bg-sage-green hover:text-white'
              }`}
            >
              {tag}
            </button>
          ))}
        </div>

        {/* Stories Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {filteredStories.map((story) => (
            <div key={story.id} className="card hover:shadow-xl transition-shadow duration-300">
              <div className="h-48 overflow-hidden">
                <img
                  src={story.image}
                  alt={story.title}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
              </div>

              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-xl font-semibold text-forest-green">{story.title}</h3>
                  <button
                    onClick={() => handleLike(story.id)}
                    className="flex items-center gap-1 text-stone-gray hover:text-red-500 transition-colors"
                  >
                    <span>❤️</span>
                    <span className="text-sm">{story.likes}</span>
                  </button>
                </div>

                <div className="flex justify-between items-center text-sm text-stone-gray mb-4">
                  <span>By {story.author}</span>
                  <span>{new Date(story.date).toLocaleDateString()}</span>
                </div>

                <p className="text-stone-gray mb-4 line-clamp-4">{story.content}</p>

                <div className="flex flex-wrap gap-2">
                  {story.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="text-xs bg-moss-green bg-opacity-20 text-forest-green px-2 py-1 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        {!showForm && (
          <div className="mt-16 text-center bg-white rounded-lg p-8 shadow-lg">
            <h2 className="text-2xl font-nature font-bold text-forest-green mb-4">
              Have an Amazing Story to Share?
            </h2>
            <p className="text-stone-gray mb-6">
              We'd love to hear about your adventure at Snawbra Campsite.
              Your story might inspire someone else's next great adventure!
            </p>
            <button
              onClick={scrollToForm}
              className="bg-gradient-to-r from-sage-green to-moss-green text-white px-8 py-4 rounded-full font-bold text-lg transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
            >
              📸 Share Your Experience
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Community;
