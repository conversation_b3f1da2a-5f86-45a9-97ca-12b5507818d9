import React, { useState, useEffect } from 'react';
import BackgroundEffects from '../../components/BackgroundEffects/BackgroundEffects';

interface BlogPost {
  id: string;
  title: string;
  author: string;
  date: string;
  content: string;
  image: string;
  tags: string[];
  votes: number;
  month: string;
  status: 'featured' | 'archived';
  socialShares: number;
  isWinner?: boolean;
}

interface MonthlyContest {
  month: string;
  year: number;
  featuredPosts: BlogPost[];
  winner?: BlogPost;
  isActive: boolean;
  endDate: Date;
  daysLeft: number;
}

const Community: React.FC = () => {
  const [userVotes, setUserVotes] = useState<Record<string, boolean>>({});
  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  const [sortBy, setSortBy] = useState<'votes' | 'recent' | 'shares'>('votes');

  // Get current month data
  const getCurrentMonth = () => {
    const now = new Date();
    return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
  };

  const getDaysUntilEndOfMonth = () => {
    const now = new Date();
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    const diffTime = endOfMonth.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Sample featured blogs for current month
  const [featuredBlogs, setFeaturedBlogs] = useState<BlogPost[]>([
    {
      id: 'blog-1',
      title: 'My First Solo Camping Adventure',
      author: 'Sarah M.',
      date: '2024-12-15',
      content: 'Last weekend, I decided to take the plunge and go on my first solo camping trip. I was nervous but excited to challenge myself and connect with nature in a completely new way. Snawbra provided the perfect safe environment for my first solo adventure...',
      image: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      tags: ['Solo Travel', 'Adventure', 'Nature'],
      votes: 247,
      month: getCurrentMonth(),
      status: 'featured',
      socialShares: 89
    },
    {
      id: 'blog-2',
      title: 'Family Fun at Snawbra',
      author: 'Mike & Lisa',
      date: '2024-12-10',
      content: 'We brought our three kids to Snawbra for a weekend getaway. The kids loved the nature trails and we made memories that will last a lifetime. The family-friendly activities were perfectly organized...',
      image: 'https://images.unsplash.com/photo-1571863533956-01c88e79957e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      tags: ['Family', 'Kids', 'Weekend'],
      votes: 189,
      month: getCurrentMonth(),
      status: 'featured',
      socialShares: 56
    },
    {
      id: 'blog-3',
      title: 'Stargazing Magic',
      author: 'Alex R.',
      date: '2024-12-08',
      content: 'The night sky at Snawbra is absolutely breathtaking. Away from city lights, you can see the Milky Way in all its glory. This experience changed my perspective on nature and our place in the universe...',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      tags: ['Stargazing', 'Night', 'Photography'],
      votes: 312,
      month: getCurrentMonth(),
      status: 'featured',
      socialShares: 134
    }
  ]);

  const currentContest: MonthlyContest = {
    month: 'December',
    year: 2024,
    featuredPosts: featuredBlogs,
    isActive: true,
    endDate: new Date('2024-12-31'),
    daysLeft: getDaysUntilEndOfMonth()
  };

  const handleVote = (blogId: string) => {
    if (userVotes[blogId]) return; // Already voted
    
    setFeaturedBlogs(prev => 
      prev.map(blog => 
        blog.id === blogId 
          ? { ...blog, votes: blog.votes + 1 }
          : blog
      )
    );
    
    setUserVotes(prev => ({ ...prev, [blogId]: true }));
    
    // Store in localStorage
    localStorage.setItem('blogVotes', JSON.stringify({ ...userVotes, [blogId]: true }));
  };

  const handleShare = (blog: BlogPost, platform: string) => {
    const url = `${window.location.origin}/community/blog/${blog.id}`;
    const text = `Check out this amazing camping story: "${blog.title}" by ${blog.author}`;
    
    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
        break;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
      
      // Update share count
      setFeaturedBlogs(prev => 
        prev.map(b => 
          b.id === blog.id 
            ? { ...b, socialShares: b.socialShares + 1 }
            : b
        )
      );
    }
  };

  const sortedBlogs = [...featuredBlogs].sort((a, b) => {
    switch (sortBy) {
      case 'votes':
        return b.votes - a.votes;
      case 'recent':
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case 'shares':
        return b.socialShares - a.socialShares;
      default:
        return 0;
    }
  });

  const topBlog = sortedBlogs[0];

  useEffect(() => {
    // Load user votes from localStorage
    const savedVotes = localStorage.getItem('blogVotes');
    if (savedVotes) {
      setUserVotes(JSON.parse(savedVotes));
    }
  }, []);

  return (
    <div className="min-h-screen py-16 bg-warm-beige relative">
      <BackgroundEffects variant="sunset" />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-nature font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-4">
            🏆 Monthly Blog Contest
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto mb-6">
            Every month, 10 amazing camping stories compete for votes. The winner gets a full weekend package at Snawbra Campsite!
          </p>
          
          {/* Contest Timer */}
          <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl p-6 max-w-md mx-auto mb-8">
            <div className="text-3xl font-bold mb-2">{currentContest.daysLeft}</div>
            <div className="text-lg">Days Left to Vote</div>
            <div className="text-sm opacity-90 mt-2">{currentContest.month} {currentContest.year} Contest</div>
          </div>
        </div>

        {/* Current Leader */}
        {topBlog && (
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-3xl p-8 mb-12 text-white relative overflow-hidden">
            <div className="absolute top-4 right-4 text-6xl opacity-20">👑</div>
            <div className="relative z-10">
              <h2 className="text-2xl font-bold mb-4">🥇 Current Leader</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
                <div>
                  <h3 className="text-3xl font-bold mb-2">{topBlog.title}</h3>
                  <p className="text-xl opacity-90 mb-4">by {topBlog.author}</p>
                  <div className="flex items-center gap-4 text-lg">
                    <span className="bg-white/20 px-4 py-2 rounded-full">
                      🗳️ {topBlog.votes} votes
                    </span>
                    <span className="bg-white/20 px-4 py-2 rounded-full">
                      📤 {topBlog.socialShares} shares
                    </span>
                  </div>
                </div>
                <div className="text-center">
                  <img 
                    src={topBlog.image} 
                    alt={topBlog.title}
                    className="w-full h-48 object-cover rounded-2xl shadow-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Sort Controls */}
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">Featured Stories</h2>
          <div className="flex gap-2">
            {[
              { key: 'votes', label: '🗳️ Most Voted', icon: '🗳️' },
              { key: 'recent', label: '🕒 Most Recent', icon: '🕒' },
              { key: 'shares', label: '📤 Most Shared', icon: '📤' }
            ].map(option => (
              <button
                key={option.key}
                onClick={() => setSortBy(option.key as any)}
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                  sortBy === option.key
                    ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                }`}
              >
                {option.icon} {option.label.split(' ').slice(1).join(' ')}
              </button>
            ))}
          </div>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {sortedBlogs.map((blog, index) => (
            <div key={blog.id} className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:scale-105">
              <div className="relative">
                <img 
                  src={blog.image} 
                  alt={blog.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  #{index + 1}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{blog.title}</h3>
                <p className="text-gray-600 mb-2">by {blog.author}</p>
                <p className="text-gray-700 text-sm mb-4 line-clamp-3">{blog.content}</p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {blog.tags.map(tag => (
                    <span key={tag} className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
                
                <div className="flex justify-between items-center">
                  <button
                    onClick={() => handleVote(blog.id)}
                    disabled={userVotes[blog.id]}
                    className={`flex items-center gap-2 px-4 py-2 rounded-full font-medium transition-all duration-300 ${
                      userVotes[blog.id]
                        ? 'bg-green-100 text-green-700 cursor-not-allowed'
                        : 'bg-gradient-to-r from-orange-500 to-red-500 text-white hover:shadow-lg transform hover:scale-105'
                    }`}
                  >
                    {userVotes[blog.id] ? '✅' : '🗳️'} {blog.votes}
                  </button>
                  
                  <div className="flex gap-1">
                    {['twitter', 'facebook', 'linkedin'].map(platform => (
                      <button
                        key={platform}
                        onClick={() => handleShare(blog, platform)}
                        className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-sm transition-colors duration-300"
                        title={`Share on ${platform}`}
                      >
                        {platform === 'twitter' ? '🐦' : platform === 'facebook' ? '📘' : '💼'}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Submit Your Story CTA */}
        <div className="bg-gradient-to-r from-forest-green to-pine-green rounded-3xl p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">📝 Share Your Adventure</h2>
          <p className="text-xl mb-6 opacity-90">
            Have an amazing camping story? Submit it for next month's contest!
          </p>
          <button
            onClick={() => setShowSubmissionForm(true)}
            className="bg-white text-forest-green px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            🚀 Submit Your Story
          </button>
        </div>
      </div>
    </div>
  );
};

export default Community;
