import React, { useState } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import type { Event, View } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = momentLocalizer(moment);

interface CampsiteEvent extends Event {
  id: number;
  title: string;
  start: Date;
  end: Date;
  description: string;
  category: string;
  capacity: number;
  registered: number;
  price: string;
  location: string;
}

// Sample events data for MVP
const events: CampsiteEvent[] = [
  {
    id: 1,
    title: 'Morning Yoga Session',
    start: new Date(2024, 11, 28, 7, 0), // December 28, 2024, 7:00 AM
    end: new Date(2024, 11, 28, 8, 30),
    description: 'Start your day with peaceful yoga in nature\'s embrace.',
    category: 'Wellness',
    capacity: 20,
    registered: 12,
    price: 'Free',
    location: 'Meadow Pavilion'
  },
  {
    id: 2,
    title: 'Campfire Cooking Workshop',
    start: new Date(2024, 11, 29, 17, 0), // December 29, 2024, 5:00 PM
    end: new Date(2024, 11, 29, 19, 0),
    description: 'Learn traditional campfire cooking techniques and recipes.',
    category: 'Workshop',
    capacity: 15,
    registered: 8,
    price: '$25',
    location: 'Fire Circle'
  },
  {
    id: 3,
    title: 'Winter Solstice Celebration',
    start: new Date(2024, 11, 30, 18, 0), // December 30, 2024, 6:00 PM
    end: new Date(2024, 11, 30, 22, 0),
    description: 'Celebrate the winter solstice with music, stories, and community.',
    category: 'Festival',
    capacity: 50,
    registered: 35,
    price: '$15',
    location: 'Main Lodge'
  },
  {
    id: 4,
    title: 'New Year\'s Stargazing',
    start: new Date(2024, 11, 31, 22, 0), // December 31, 2024, 10:00 PM
    end: new Date(2025, 0, 1, 1, 0), // January 1, 2025, 1:00 AM
    description: 'Ring in the new year under the stars with guided astronomy.',
    category: 'Special Event',
    capacity: 30,
    registered: 22,
    price: '$20',
    location: 'Observatory Deck'
  },
  {
    id: 5,
    title: 'Nature Photography Walk',
    start: new Date(2025, 0, 2, 9, 0), // January 2, 2025, 9:00 AM
    end: new Date(2025, 0, 2, 12, 0),
    description: 'Capture winter\'s beauty with professional photography guidance.',
    category: 'Workshop',
    capacity: 12,
    registered: 7,
    price: '$35',
    location: 'Forest Trails'
  },
  {
    id: 6,
    title: 'Family Game Night',
    start: new Date(2025, 0, 3, 19, 0), // January 3, 2025, 7:00 PM
    end: new Date(2025, 0, 3, 21, 30),
    description: 'Fun board games and activities for the whole family.',
    category: 'Family',
    capacity: 25,
    registered: 18,
    price: 'Free',
    location: 'Recreation Hall'
  },
  {
    id: 7,
    title: 'Wilderness First Aid Training',
    start: new Date(2025, 0, 5, 10, 0), // January 5, 2025, 10:00 AM
    end: new Date(2025, 0, 5, 16, 0),
    description: 'Essential first aid skills for outdoor enthusiasts.',
    category: 'Training',
    capacity: 16,
    registered: 11,
    price: '$75',
    location: 'Training Center'
  }
];

const Events: React.FC = () => {
  const [view, setView] = useState<View>('month');

  const handleSelectEvent = (event: CampsiteEvent) => {
    console.log('Event selected:', event);
    // TODO: Implement event details modal
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Wellness': return '#10B981'; // green
      case 'Workshop': return '#F59E0B'; // amber
      case 'Festival': return '#EF4444'; // red
      case 'Special Event': return '#8B5CF6'; // purple
      case 'Family': return '#06B6D4'; // cyan
      case 'Training': return '#6366F1'; // indigo
      default: return '#6B7280'; // gray
    }
  };

  const eventStyleGetter = (event: CampsiteEvent) => {
    return {
      style: {
        backgroundColor: getCategoryColor(event.category),
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  return (
    <div className="min-h-screen py-16 bg-warm-beige">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-nature font-bold text-forest-green mb-4">
            Events Calendar
          </h1>
          <p className="text-xl text-stone-gray max-w-3xl mx-auto">
            Join our community events, workshops, and seasonal celebrations.
          </p>
        </div>

        {/* Calendar Controls */}
        <div className="mb-8 flex flex-wrap justify-between items-center bg-white rounded-lg p-4 shadow-md">
          <div className="flex gap-2 mb-4 sm:mb-0">
            <button
              onClick={() => setView('month')}
              className={`px-4 py-2 rounded ${view === 'month' ? 'bg-forest-green text-white' : 'bg-gray-200 text-stone-gray'}`}
            >
              Month
            </button>
            <button
              onClick={() => setView('week')}
              className={`px-4 py-2 rounded ${view === 'week' ? 'bg-forest-green text-white' : 'bg-gray-200 text-stone-gray'}`}
            >
              Week
            </button>
            <button
              onClick={() => setView('day')}
              className={`px-4 py-2 rounded ${view === 'day' ? 'bg-forest-green text-white' : 'bg-gray-200 text-stone-gray'}`}
            >
              Day
            </button>
          </div>

          {/* Legend */}
          <div className="flex flex-wrap gap-4 text-sm">
            {['Wellness', 'Workshop', 'Festival', 'Special Event', 'Family', 'Training'].map((category) => (
              <div key={category} className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: getCategoryColor(category) }}
                ></div>
                <span className="text-stone-gray">{category}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Calendar */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <Calendar
            localizer={localizer}
            events={events}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 600 }}
            view={view}
            onView={setView}
            onSelectEvent={handleSelectEvent}
            eventPropGetter={eventStyleGetter}
            popup
            showMultiDayTimes
            step={60}
            showAllEvents
          />
        </div>
      </div>
    </div>
  );
};

export default Events;
