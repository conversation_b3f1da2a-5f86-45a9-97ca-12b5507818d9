{"version": 1.1, "atDirectives": [{"name": "@apply", "description": "Use @apply to inline any existing utility classes into your own custom CSS.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@layer", "description": "Use @layer to tell Tailwind which \"bucket\" a set of custom styles belong to.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#layer"}]}, {"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, utilities and variants styles into your CSS.", "references": [{"name": "Tailwind CSS Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#tailwind"}]}]}