# 🔥 Firebase Backend Architecture for Snawbra Campsite

## Overview
This document outlines the Firebase backend architecture designed to support the Snawbra Campsite website. The architecture is designed for scalability, real-time features, and comprehensive content management.

## 🏗️ Firebase Services Required

### 1. Firestore Database
**Purpose**: Primary database for all application data
**Plan**: Pay-as-you-go (suitable for MVP and scaling)

### 2. Firebase Authentication
**Purpose**: User authentication and authorization
**Plan**: Free tier (up to 10,000 monthly active users)

### 3. Firebase Storage
**Purpose**: File storage for images, documents, and media
**Plan**: Pay-as-you-go (5GB free)

### 4. Firebase Hosting
**Purpose**: Static website hosting with CDN
**Plan**: Free tier (10GB storage, 125 operations/day)

### 5. Cloud Functions
**Purpose**: Server-side logic and API endpoints
**Plan**: Pay-as-you-go (2M invocations/month free)

## 📊 Database Schema (Firestore)

### Collections Structure

#### 1. Users Collection (`users`)
```javascript
{
  uid: string,                    // Firebase Auth UID
  email: string,
  name: string,
  role: 'admin' | 'user',
  avatar?: string,
  preferences: {
    tripType?: string,
    interests?: string[],
    accommodationType?: string,
    groupSize?: number,
    budget?: string,
    activities?: string[]
  },
  createdAt: timestamp,
  lastLoginAt: timestamp
}
```

#### 2. Blog Submissions Collection (`blogSubmissions`)
```javascript
{
  id: string,
  title: string,
  author: string,
  authorId: string,              // Reference to user UID
  content: string,
  tags: string[],
  photoUrl?: string,
  status: 'pending' | 'approved' | 'rejected' | 'featured',
  submittedAt: timestamp,
  reviewedAt?: timestamp,
  reviewedBy?: string,           // Admin UID
  votes: number,
  socialShares: number,
  adminNotes?: string,
  featuredMonth?: string,        // YYYY-MM format
  votedBy: string[]              // Array of user UIDs who voted
}
```

#### 3. Events Collection (`events`)
```javascript
{
  id: string,
  title: string,
  description: string,
  category: string,
  start: timestamp,
  end: timestamp,
  duration: string,
  capacity: number,
  price: string,
  location: string,
  organizer: string,
  organizerId?: string,          // Reference to user UID
  status: 'active' | 'cancelled' | 'completed',
  registrations: number,
  createdAt: timestamp,
  updatedAt: timestamp
}
```

#### 4. Event Proposals Collection (`eventProposals`)
```javascript
{
  id: string,
  title: string,
  description: string,
  category: string,
  proposedDate: timestamp,
  duration: string,
  expectedCapacity: number,
  proposedPrice: string,
  organizerName: string,
  organizerEmail: string,
  organizerPhone?: string,
  organizerId?: string,          // Reference to user UID if logged in
  status: 'pending' | 'approved' | 'rejected',
  adminNotes?: string,
  submittedAt: timestamp,
  reviewedAt?: timestamp,
  reviewedBy?: string            // Admin UID
}
```

#### 5. Contact Submissions Collection (`contactSubmissions`)
```javascript
{
  id: string,
  name: string,
  email: string,
  phone?: string,
  subject: string,
  message: string,
  preferredContact: 'email' | 'phone',
  status: 'pending' | 'responded' | 'resolved',
  submittedAt: timestamp,
  respondedAt?: timestamp,
  respondedBy?: string,          // Admin UID
  adminNotes?: string
}
```

#### 6. Analytics Collection (`analytics`)
```javascript
{
  id: string,
  type: 'call_click' | 'page_view' | 'form_submission' | 'blog_vote',
  userId?: string,               // If user is logged in
  sessionId: string,
  data: object,                  // Event-specific data
  timestamp: timestamp,
  userAgent?: string,
  ipAddress?: string             // For analytics (anonymized)
}
```

#### 7. Site Settings Collection (`siteSettings`)
```javascript
{
  id: 'global',
  monthlyBlogLimit: number,      // Default: 10
  currentMonth: string,          // YYYY-MM format
  featuredPostsCount: number,
  maintenanceMode: boolean,
  announcementBanner?: {
    text: string,
    type: 'info' | 'warning' | 'success',
    active: boolean
  },
  contactInfo: {
    phone: string,
    email: string,
    address: object
  },
  updatedAt: timestamp,
  updatedBy: string              // Admin UID
}
```

## 🔐 Security Rules

### Firestore Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
                     resource.data.role == 'admin' && 
                     request.auth.token.role == 'admin';
    }
    
    // Blog submissions
    match /blogSubmissions/{submissionId} {
      allow read: if true; // Public read for featured posts
      allow create: if request.auth != null;
      allow update: if request.auth != null && 
                       (request.auth.uid == resource.data.authorId || 
                        request.auth.token.role == 'admin');
    }
    
    // Events - public read, admin write
    match /events/{eventId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.role == 'admin';
    }
    
    // Event proposals
    match /eventProposals/{proposalId} {
      allow read: if request.auth != null && request.auth.token.role == 'admin';
      allow create: if true; // Anyone can submit proposals
      allow update: if request.auth != null && request.auth.token.role == 'admin';
    }
    
    // Contact submissions - admin only
    match /contactSubmissions/{submissionId} {
      allow read, write: if request.auth != null && request.auth.token.role == 'admin';
      allow create: if true; // Anyone can submit contact forms
    }
    
    // Analytics - write only, admin read
    match /analytics/{analyticsId} {
      allow create: if true;
      allow read: if request.auth != null && request.auth.token.role == 'admin';
    }
    
    // Site settings - admin only
    match /siteSettings/{settingId} {
      allow read: if true; // Public settings like contact info
      allow write: if request.auth != null && request.auth.token.role == 'admin';
    }
  }
}
```

## ⚡ Cloud Functions

### Required Functions

#### 1. User Role Management
```javascript
// functions/src/auth.js
exports.setUserRole = functions.auth.user().onCreate(async (user) => {
  // Set default role and create user document
});

exports.setAdminRole = functions.https.onCall(async (data, context) => {
  // Admin function to set user roles
});
```

#### 2. Blog Contest Management
```javascript
// functions/src/blog.js
exports.processBlogVote = functions.firestore
  .document('blogSubmissions/{submissionId}')
  .onUpdate(async (change, context) => {
    // Handle vote counting and validation
  });

exports.resetMonthlyContest = functions.pubsub
  .schedule('0 0 1 * *') // First day of each month
  .onRun(async (context) => {
    // Reset monthly contest, archive previous winners
  });
```

#### 3. Analytics Processing
```javascript
// functions/src/analytics.js
exports.processAnalytics = functions.firestore
  .document('analytics/{analyticsId}')
  .onCreate(async (snap, context) => {
    // Process and aggregate analytics data
  });
```

#### 4. Email Notifications
```javascript
// functions/src/notifications.js
exports.sendContactNotification = functions.firestore
  .document('contactSubmissions/{submissionId}')
  .onCreate(async (snap, context) => {
    // Send email notification to admin
  });

exports.sendEventApprovalNotification = functions.firestore
  .document('eventProposals/{proposalId}')
  .onUpdate(async (change, context) => {
    // Notify organizer of approval/rejection
  });
```

## 🚀 Implementation Steps

### Phase 1: Setup & Authentication
1. Create Firebase project
2. Configure Authentication (Email/Password)
3. Set up Firestore database
4. Implement user registration/login
5. Configure admin role system

### Phase 2: Core Features
1. Migrate blog submission system
2. Implement event management
3. Set up contact form backend
4. Configure file upload for images

### Phase 3: Advanced Features
1. Implement analytics tracking
2. Set up email notifications
3. Add real-time features
4. Configure automated backups

### Phase 4: Optimization
1. Implement caching strategies
2. Optimize security rules
3. Set up monitoring and alerts
4. Performance optimization

## 💰 Cost Estimation

### Monthly Costs (Estimated)
- **Firestore**: $0-25 (based on reads/writes)
- **Authentication**: Free (under 10K users)
- **Storage**: $0-10 (based on file uploads)
- **Cloud Functions**: $0-15 (based on invocations)
- **Hosting**: Free (under limits)

**Total Estimated**: $0-50/month for MVP, scaling with usage

## 🔧 Environment Configuration

### Required Environment Variables
```env
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
```

## 📋 Migration Checklist

- [ ] Create Firebase project
- [ ] Configure authentication
- [ ] Set up Firestore collections
- [ ] Implement security rules
- [ ] Deploy Cloud Functions
- [ ] Migrate localStorage data
- [ ] Test all features
- [ ] Set up monitoring
- [ ] Configure backups
- [ ] Update environment variables

## 🚨 Important Notes

1. **Data Migration**: Current localStorage data will need to be migrated to Firestore
2. **Security**: Implement proper validation in Cloud Functions
3. **Backup**: Set up automated daily backups
4. **Monitoring**: Use Firebase Analytics and Performance Monitoring
5. **Scaling**: Monitor usage and adjust Firebase plans accordingly

This architecture provides a solid foundation for the Snawbra Campsite website with room for future growth and feature additions.
