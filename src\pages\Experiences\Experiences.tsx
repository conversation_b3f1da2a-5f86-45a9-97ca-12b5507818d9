import React, { useState } from 'react';

interface Experience {
  id: number;
  name: string;
  category: string;
  duration: string;
  difficulty: string;
  price: string;
  image: string;
  description: string;
  highlights: string[];
  included: string[];
  maxParticipants: number;
}

const experiences: Experience[] = [
  {
    id: 1,
    name: 'Guided Nature Trail',
    category: 'Hiking',
    duration: '2 hours',
    difficulty: 'Easy',
    price: '$25/person',
    image: '/Assets/HikeInForest.jpg',
    description: 'Discover the hidden wonders of our forest with an expert naturalist guide.',
    highlights: ['Wildlife spotting', 'Plant identification', 'Photography opportunities', 'Local history'],
    included: ['Professional guide', 'Trail map', 'Binoculars', 'Field notebook'],
    maxParticipants: 12
  },
  {
    id: 2,
    name: 'Horseback Adventure',
    category: 'Riding',
    duration: '3 hours',
    difficulty: 'Moderate',
    price: '$75/person',
    image: '/Assets/CedarHikeWHorses.jpg',
    description: 'Explore scenic mountain trails on horseback with breathtaking valley views.',
    highlights: ['Mountain vistas', 'Cedar forest trails', 'Horse grooming', 'Western riding'],
    included: ['Horse & equipment', 'Safety briefing', 'Professional instructor', 'Trail snacks'],
    maxParticipants: 8
  },
  {
    id: 3,
    name: 'Campfire Stories & Stargazing',
    category: 'Evening',
    duration: '2.5 hours',
    difficulty: 'Easy',
    price: '$15/person',
    image: '/Assets/Bonfire.jpg',
    description: 'Gather around the campfire for stories, songs, and spectacular stargazing.',
    highlights: ['Traditional campfire', 'Local legends', 'Constellation tours', 'S\'mores making'],
    included: ['Campfire setup', 'Storyteller', 'Telescope access', 'Hot cocoa'],
    maxParticipants: 20
  },
  {
    id: 4,
    name: 'Forest Photography Workshop',
    category: 'Photography',
    duration: '4 hours',
    difficulty: 'Moderate',
    price: '$65/person',
    image: '/Assets/PineCloseUp.jpg',
    description: 'Learn nature photography techniques while capturing the beauty of our wilderness.',
    highlights: ['Macro photography', 'Landscape composition', 'Wildlife photography', 'Golden hour shots'],
    included: ['Professional photographer', 'Equipment tips', 'Editing basics', 'Digital gallery'],
    maxParticipants: 6
  },
  {
    id: 5,
    name: 'Wilderness Survival Skills',
    category: 'Adventure',
    duration: '6 hours',
    difficulty: 'Challenging',
    price: '$95/person',
    image: '/Assets/CampingWFriends.jpg',
    description: 'Learn essential wilderness survival techniques in a safe, controlled environment.',
    highlights: ['Fire making', 'Shelter building', 'Water purification', 'Navigation skills'],
    included: ['Survival expert', 'All materials', 'Lunch', 'Certificate'],
    maxParticipants: 10
  },
  {
    id: 6,
    name: 'Meditation & Mindfulness',
    category: 'Wellness',
    duration: '1.5 hours',
    difficulty: 'Easy',
    price: '$30/person',
    image: '/Assets/PersonOnHammock.jpg',
    description: 'Find inner peace through guided meditation in nature\'s embrace.',
    highlights: ['Guided meditation', 'Breathing exercises', 'Nature sounds', 'Mindfulness techniques'],
    included: ['Certified instructor', 'Meditation mat', 'Herbal tea', 'Take-home guide'],
    maxParticipants: 15
  }
];

const Experiences: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');

  const categories = ['All', 'Hiking', 'Riding', 'Evening', 'Photography', 'Adventure', 'Wellness'];

  const filteredExperiences = selectedCategory === 'All'
    ? experiences
    : experiences.filter(exp => exp.category === selectedCategory);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen py-16 bg-warm-beige">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-nature font-bold text-forest-green mb-4">
            Experiences
          </h1>
          <p className="text-xl text-stone-gray max-w-3xl mx-auto">
            Immerse yourself in nature with our curated outdoor experiences and activities.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-2 rounded-full font-medium transition-colors duration-300 ${
                selectedCategory === category
                  ? 'bg-forest-green text-white'
                  : 'bg-white text-stone-gray hover:bg-sage-green hover:text-white'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Experiences Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredExperiences.map((experience) => (
            <div
              key={experience.id}
              className="card hover:shadow-xl transition-shadow duration-300"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={experience.image}
                  alt={experience.name}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-forest-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {experience.price}
                </div>
                <div className={`absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-semibold ${getDifficultyColor(experience.difficulty)}`}>
                  {experience.difficulty}
                </div>
              </div>

              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-xl font-semibold text-forest-green">{experience.name}</h3>
                  <span className="text-sm text-stone-gray bg-sage-green bg-opacity-20 px-2 py-1 rounded">
                    {experience.category}
                  </span>
                </div>

                <p className="text-stone-gray text-sm mb-4">{experience.description}</p>

                <div className="flex items-center justify-between text-sm text-stone-gray mb-4">
                  <span>⏱️ {experience.duration}</span>
                  <span>👥 Max {experience.maxParticipants}</span>
                </div>

                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-forest-green mb-2">Highlights:</h4>
                  <div className="flex flex-wrap gap-1">
                    {experience.highlights.slice(0, 3).map((highlight, index) => (
                      <span
                        key={index}
                        className="text-xs bg-moss-green bg-opacity-20 text-forest-green px-2 py-1 rounded"
                      >
                        {highlight}
                      </span>
                    ))}
                    {experience.highlights.length > 3 && (
                      <span className="text-xs text-stone-gray">
                        +{experience.highlights.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                <button className="w-full btn-primary text-sm">
                  Book Experience
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center bg-white rounded-lg p-8 shadow-lg">
          <h2 className="text-2xl font-nature font-bold text-forest-green mb-4">
            Can't Find What You're Looking For?
          </h2>
          <p className="text-stone-gray mb-6">
            We offer custom experiences tailored to your interests and skill level.
            Contact us to create your perfect adventure.
          </p>
          <button className="btn-secondary">
            Request Custom Experience
          </button>
        </div>
      </div>
    </div>
  );
};

export default Experiences;
