import React from 'react';

interface BackgroundEffectsProps {
  variant?: 'forest' | 'mountain' | 'lake' | 'sunset' | 'stars';
}

const BackgroundEffects: React.FC<BackgroundEffectsProps> = ({
  variant = 'forest'
}) => {
  const getBackgroundStyle = () => {
    switch (variant) {
      case 'forest':
        return {
          background: `
            linear-gradient(135deg, rgba(34, 139, 34, 0.08) 0%, rgba(0, 100, 0, 0.03) 100%),
            radial-gradient(circle at 20% 80%, rgba(34, 139, 34, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(47, 79, 47, 0.1) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23228B22' fill-opacity='0.02'%3E%3Cpath d='M60 60c16.569 0 30-13.431 30-30S76.569 0 60 0 30 13.431 30 30s13.431 30 30 30zm0-60c16.569 0 30 13.431 30 30S76.569 60 60 60 30 46.569 30 30 43.431 0 60 0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
          `
        };
      case 'mountain':
        return {
          background: `
            linear-gradient(135deg, rgba(105, 105, 105, 0.08) 0%, rgba(169, 169, 169, 0.03) 100%),
            radial-gradient(circle at 30% 70%, rgba(105, 105, 105, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 70% 30%, rgba(169, 169, 169, 0.1) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23696969' fill-opacity='0.02'%3E%3Cpath d='M50 0L75 50H25z'/%3E%3Cpath d='M25 50L50 100H0z'/%3E%3Cpath d='M75 50L100 100H50z'/%3E%3C/g%3E%3C/svg%3E")
          `
        };
      case 'lake':
        return {
          background: `
            linear-gradient(135deg, rgba(70, 130, 180, 0.08) 0%, rgba(135, 206, 235, 0.03) 100%),
            radial-gradient(circle at 40% 60%, rgba(70, 130, 180, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 60% 40%, rgba(135, 206, 235, 0.1) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%234682B4' fill-opacity='0.02'%3E%3Ccircle cx='40' cy='40' r='20'/%3E%3Ccircle cx='20' cy='20' r='10'/%3E%3Ccircle cx='60' cy='20' r='10'/%3E%3Ccircle cx='20' cy='60' r='10'/%3E%3Ccircle cx='60' cy='60' r='10'/%3E%3C/g%3E%3C/svg%3E")
          `
        };
      case 'sunset':
        return {
          background: `
            linear-gradient(135deg, rgba(255, 165, 0, 0.08) 0%, rgba(255, 69, 0, 0.03) 100%),
            radial-gradient(circle at 25% 75%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 25%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='90' height='90' viewBox='0 0 90 90' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23FFA500' fill-opacity='0.02'%3E%3Cpath d='M45 0L60 30L90 45L60 60L45 90L30 60L0 45L30 30z'/%3E%3C/g%3E%3C/svg%3E")
          `
        };
      case 'stars':
        return {
          background: `
            linear-gradient(135deg, rgba(25, 25, 112, 0.08) 0%, rgba(72, 61, 139, 0.03) 100%),
            radial-gradient(circle at 35% 65%, rgba(25, 25, 112, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 65% 35%, rgba(72, 61, 139, 0.1) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='110' height='110' viewBox='0 0 110 110' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23191970' fill-opacity='0.02'%3E%3Cpath d='M55 0l5 20 20-5-5 20 20 5-20 5 5 20-20-5-5 20-5-20-20 5 5-20-20-5 20-5-5-20 20 5z'/%3E%3C/g%3E%3C/svg%3E")
          `
        };
      default:
        return { background: 'transparent' };
    }
  };

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {/* Beautiful Static Background */}
      <div
        className="absolute inset-0"
        style={getBackgroundStyle()}
      />

      {/* Subtle Animated Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent animate-pulse"
           style={{ animationDuration: '4s' }} />

      {/* Bottom Fade */}
      <div className="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-white/30 to-transparent" />
    </div>
  );
};

export default BackgroundEffects;
