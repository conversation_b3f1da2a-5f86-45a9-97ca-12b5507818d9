import React, { useEffect, useState } from 'react';

interface FloatingIcon {
  id: number;
  icon: string;
  x: number;
  y: number;
  size: number;
  speed: number;
  direction: number;
}

interface BackgroundEffectsProps {
  variant?: 'forest' | 'mountain' | 'lake' | 'sunset' | 'stars';
  showFloatingIcons?: boolean;
}

const BackgroundEffects: React.FC<BackgroundEffectsProps> = ({ 
  variant = 'forest', 
  showFloatingIcons = true 
}) => {
  const [floatingIcons, setFloatingIcons] = useState<FloatingIcon[]>([]);

  const iconSets = {
    forest: ['🌲', '🍃', '🦋', '🐿️', '🌿', '🦅', '🍄'],
    mountain: ['⛰️', '🏔️', '🦅', '🌨️', '☁️', '🌟', '🏕️'],
    lake: ['🌊', '🐟', '🦆', '🌅', '🏞️', '🚣', '💧'],
    sunset: ['🌅', '🌇', '☀️', '🧡', '💛', '🌤️', '✨'],
    stars: ['⭐', '🌟', '✨', '🌙', '🌌', '💫', '🔭']
  };

  const backgroundImages = {
    forest: 'linear-gradient(135deg, rgba(34, 139, 34, 0.1) 0%, rgba(0, 100, 0, 0.05) 100%), url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23228B22" fill-opacity="0.03"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    mountain: 'linear-gradient(135deg, rgba(105, 105, 105, 0.1) 0%, rgba(169, 169, 169, 0.05) 100%), url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23696969" fill-opacity="0.03" fill-rule="evenodd"%3E%3Cpath d="M20 20c0 11.046-8.954 20-20 20v20h40V20c0-11.046-8.954-20-20-20z"/%3E%3C/g%3E%3C/svg%3E")',
    lake: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(135, 206, 235, 0.05) 100%), url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%234682B4" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3Ccircle cx="10" cy="10" r="4"/%3E%3Ccircle cx="50" cy="10" r="4"/%3E%3Ccircle cx="10" cy="50" r="4"/%3E%3Ccircle cx="50" cy="50" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
    sunset: 'linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(255, 69, 0, 0.05) 100%), url("data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23FFA500" fill-opacity="0.03" fill-rule="evenodd"%3E%3Cpath d="M40 40c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm-20 0c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20z"/%3E%3C/g%3E%3C/svg%3E")',
    stars: 'linear-gradient(135deg, rgba(25, 25, 112, 0.1) 0%, rgba(72, 61, 139, 0.05) 100%), url("data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23191970" fill-opacity="0.03"%3E%3Cpolygon points="50,0 60,40 100,50 60,60 50,100 40,60 0,50 40,40"/%3E%3C/g%3E%3C/svg%3E")'
  };

  useEffect(() => {
    if (!showFloatingIcons) return;

    const icons = iconSets[variant];
    const newIcons: FloatingIcon[] = [];

    for (let i = 0; i < 8; i++) {
      newIcons.push({
        id: i,
        icon: icons[Math.floor(Math.random() * icons.length)],
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        size: Math.random() * 20 + 15,
        speed: Math.random() * 0.5 + 0.2,
        direction: Math.random() * Math.PI * 2
      });
    }

    setFloatingIcons(newIcons);

    const animateIcons = () => {
      setFloatingIcons(prevIcons => 
        prevIcons.map(icon => {
          let newX = icon.x + Math.cos(icon.direction) * icon.speed;
          let newY = icon.y + Math.sin(icon.direction) * icon.speed;

          // Bounce off edges
          if (newX <= 0 || newX >= window.innerWidth - icon.size) {
            icon.direction = Math.PI - icon.direction;
            newX = Math.max(0, Math.min(window.innerWidth - icon.size, newX));
          }
          if (newY <= 0 || newY >= window.innerHeight - icon.size) {
            icon.direction = -icon.direction;
            newY = Math.max(0, Math.min(window.innerHeight - icon.size, newY));
          }

          return {
            ...icon,
            x: newX,
            y: newY
          };
        })
      );
    };

    const interval = setInterval(animateIcons, 50);
    return () => clearInterval(interval);
  }, [variant, showFloatingIcons]);

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {/* Background Pattern */}
      <div 
        className="absolute inset-0 opacity-30"
        style={{ 
          background: backgroundImages[variant],
          backgroundSize: '60px 60px'
        }}
      />
      
      {/* Floating Icons */}
      {showFloatingIcons && floatingIcons.map(icon => (
        <div
          key={icon.id}
          className="absolute transition-all duration-1000 ease-in-out animate-pulse"
          style={{
            left: `${icon.x}px`,
            top: `${icon.y}px`,
            fontSize: `${icon.size}px`,
            transform: `rotate(${Math.sin(Date.now() * 0.001 + icon.id) * 10}deg)`,
            animation: `float-${icon.id} ${3 + icon.id * 0.5}s ease-in-out infinite alternate`
          }}
        >
          {icon.icon}
        </div>
      ))}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes float-0 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-10px) rotate(5deg); } }
        @keyframes float-1 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-15px) rotate(-5deg); } }
        @keyframes float-2 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-8px) rotate(3deg); } }
        @keyframes float-3 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-12px) rotate(-3deg); } }
        @keyframes float-4 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-6px) rotate(4deg); } }
        @keyframes float-5 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-14px) rotate(-4deg); } }
        @keyframes float-6 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-9px) rotate(2deg); } }
        @keyframes float-7 { 0% { transform: translateY(0px) rotate(0deg); } 100% { transform: translateY(-11px) rotate(-2deg); } }
      `}</style>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white opacity-20" />
    </div>
  );
};

export default BackgroundEffects;
