{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [".vscode/css_custom_data.json"], "tailwindCSS.includeLanguages": {"css": "css", "html": "html", "javascript": "javascript", "typescript": "typescript", "javascriptreact": "javascript", "typescriptreact": "typescript"}, "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([a-zA-Z0-9\\-:]+)"]], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "editor.inlineSuggest.enabled": true}