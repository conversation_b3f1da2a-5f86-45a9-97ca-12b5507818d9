# 🏕️ Snawbra Campsite

A modern, immersive camping and nature retreat website built with React, TypeScript, and Tailwind CSS.

## ✨ Features

- **Responsive Design**: Beautiful, mobile-first design optimized for all devices
- **Interactive Calendar**: Event management with react-big-calendar
- **Admin Dashboard**: Comprehensive content management system
- **Monthly Blog Contest**: Community-driven content with voting system
- **AI Chat Assistant**: Personalized camping recommendations
- **Call Analytics**: Track user engagement with contact features
- **Demo Authentication**: Quick access for testing admin and user features

## 🚀 Tech Stack

- **Frontend**: React 19 + TypeScript + Vite
- **Styling**: Tailwind CSS with custom nature theme
- **Routing**: React Router DOM
- **Calendar**: react-big-calendar with Moment.js
- **State Management**: React Context API
- **Build Tool**: Vite with hot module replacement

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd snawbra-campsite
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Demo Access
- **Admin Access**: Use the demo login to access admin features
- **User Access**: Browse as a regular visitor

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── context/            # React Context providers
├── types/              # TypeScript type definitions
└── index.css           # Global styles and Tailwind imports
```

## 🎨 Customization

The project uses a custom Tailwind theme with nature-inspired colors:
- Forest Green: `#2d5016`
- Sage Green: `#87a96b`
- Pine Green: `#355e3b`
- Earth Brown: `#8b4513`
- Warm Beige: `#f5f5dc`

## 📱 Features Overview

### Public Features
- **Homepage**: Hero section with immersive background
- **Accommodations**: Browse camping options with filtering
- **Experiences**: Discover activities and adventures
- **Events**: Interactive calendar with event details
- **Community**: Monthly blog contest with voting
- **Contact**: Enhanced contact form with call analytics

### Admin Features
- **Dashboard**: Analytics and overview
- **Blog Contest Management**: Approve and feature posts (10/month limit)
- **Event Proposals**: Review and manage event submissions
- **Content Management**: Comprehensive admin tools
- **Analytics**: User engagement tracking

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### ESLint Configuration

For production applications, consider enabling type-aware lint rules:

```js
export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      ...tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      ...tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      ...tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs['recommended-typescript'],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```
