import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAppContext } from '../../context/AppContext';
import DemoLogin from '../DemoLogin/DemoLogin';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showDemoLogin, setShowDemoLogin] = useState(false);
  const { isLoggedIn, setShowWelcomeModal, setIsLoggedIn, isAuthenticated, user, setIsAuthenticated, setUser } = useAppContext();
  const location = useLocation();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Accommodations', path: '/accommodations' },
    { name: 'Experiences', path: '/experiences' },
    { name: 'Events', path: '/events' },
    { name: 'Community', path: '/community' },
    { name: 'Contact', path: '/contact' },
  ];

  const isActive = (path: string) => location.pathname === path;

  const handleSignInClick = () => {
    setShowWelcomeModal(true);
  };

  const handleDemoLoginClick = () => {
    setShowDemoLogin(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setIsAuthenticated(false);
    setUser(null);
    localStorage.removeItem('snawbra_visited');
    localStorage.removeItem('demoUser');
  };

  return (
    <header className="bg-white shadow-lg sticky top-0 z-30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link to="/" className="flex items-center">
            <div className="text-2xl font-nature font-bold text-forest-green">
              Snawbra Campsite
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`font-medium transition-colors duration-200 ${
                  isActive(item.path)
                    ? 'text-forest-green border-b-2 border-forest-green'
                    : 'text-stone-gray hover:text-forest-green'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* User Status */}
          <div className="hidden md:flex items-center space-x-4">
            {isLoggedIn ? (
              <div className="flex items-center space-x-3">
                <span className="text-sm text-stone-gray">Welcome back!</span>
                <div className="relative group">
                  <div className="w-8 h-8 bg-forest-green rounded-full flex items-center justify-center cursor-pointer">
                    <span className="text-white text-sm font-semibold">U</span>
                  </div>
                  {/* Dropdown menu */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-2">
                      <button
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-stone-gray hover:bg-gray-50 hover:text-forest-green transition-colors"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSignInClick}
                  className="relative overflow-hidden group bg-gradient-to-r from-forest-green to-pine-green text-white px-6 py-3 rounded-full font-bold text-sm transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <span className="relative z-10 flex items-center space-x-2">
                    <span>🌲 Join Adventure</span>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-pine-green to-sage-green opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <button
                  onClick={handleDemoLoginClick}
                  className="relative overflow-hidden group bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-full font-bold text-sm transform transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <span className="relative z-10 flex items-center space-x-2">
                    <span>🚀 Demo Login</span>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden p-2"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg
              className="w-6 h-6 text-stone-gray"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              {isMenuOpen ? (
                <path d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`font-medium transition-colors duration-200 ${
                    isActive(item.path)
                      ? 'text-forest-green'
                      : 'text-stone-gray hover:text-forest-green'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              {!isLoggedIn && (
                <button
                  onClick={handleSignInClick}
                  className="bg-gradient-to-r from-forest-green to-pine-green text-white px-6 py-3 rounded-full font-bold text-sm w-fit transform transition-all duration-300 hover:scale-105"
                >
                  🌲 Join the Adventure
                </button>
              )}
            </nav>
          </div>
        )}
      </div>

      {/* Demo Login Modal */}
      {showDemoLogin && (
        <DemoLogin onClose={() => setShowDemoLogin(false)} />
      )}
    </header>
  );
};

export default Header;
