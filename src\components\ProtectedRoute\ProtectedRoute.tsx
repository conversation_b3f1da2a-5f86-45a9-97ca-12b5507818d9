import React from 'react';
import { useAppContext } from '../../context/AppContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'admin' | 'user';
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole = 'user',
  fallback 
}) => {
  const { isAuthenticated, user } = useAppContext();

  // Check if user is authenticated
  if (!isAuthenticated || !user) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest-green to-pine-green">
        <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl">
          <div className="text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Restricted</h1>
          <p className="text-gray-600 mb-6">
            You need to be logged in to access this area. Please use the demo login to continue.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/'}
              className="w-full bg-gradient-to-r from-forest-green to-pine-green text-white py-3 px-6 rounded-full font-bold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
            >
              🏠 Return to Home
            </button>
            <p className="text-sm text-gray-500">
              Look for the "🚀 Demo Login" button in the header to get instant access
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has required role
  if (requiredRole === 'admin' && user.role !== 'admin') {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600">
        <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl">
          <div className="text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Admin Access Required</h1>
          <p className="text-gray-600 mb-6">
            This area requires administrator privileges. You are currently logged in as a regular user.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/'}
              className="w-full bg-gradient-to-r from-forest-green to-pine-green text-white py-3 px-6 rounded-full font-bold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
            >
              🏠 Return to Home
            </button>
            <p className="text-sm text-gray-500">
              Use the demo login with admin credentials to access this area
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
