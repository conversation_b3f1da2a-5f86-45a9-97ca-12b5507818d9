# Tailwind CSS Setup for Snawbra Campsite

## Overview
This project uses Tailwind CSS for styling with a custom configuration optimized for a nature/camping theme.

## Configuration Files

### Core Configuration
- `tailwind.config.js` - Main Tailwind configuration with custom colors and fonts
- `postcss.config.js` - PostCSS configuration for processing Tailwind directives
- `src/index.css` - Main CSS file with Tailwind imports and custom styles

### IDE Support
- `.vscode/settings.json` - VSCode settings for Tailwind CSS IntelliSense
- `.vscode/extensions.json` - Recommended extensions
- `.vscode/css_custom_data.json` - Custom CSS data for Tailwind directives
- `src/types/css.d.ts` - TypeScript declarations for CSS modules

## Custom Theme

### Colors
- `forest-green`: #2d5016 - Primary brand color
- `sage-green`: #87a96b - Secondary green
- `pine-green`: #355e3b - Hover states
- `moss-green`: #8fbc8f - Accent green
- `earth-brown`: #8b4513 - Earth tones
- `bark-brown`: #654321 - Dark brown
- `warm-beige`: #f5f5dc - Background
- `stone-gray`: #696969 - Text color

### Fonts
- `font-nature`: Georgia serif for headings
- `font-modern`: Inter sans-serif for body text

## Custom Components
The CSS includes custom component classes:
- `.btn-primary` - Primary button styling
- `.btn-secondary` - Secondary button styling
- `.card` - Card component styling
- `.hero-overlay` - Hero section overlay
- `.loading-screen` - Loading screen styles
- `.chat-bubble` - Chat widget styling
- `.modal-overlay` & `.modal-content` - Modal styling

## Usage
Use Tailwind utilities throughout the React components. Custom colors and fonts are available as utilities:
- `bg-forest-green`, `text-forest-green`
- `font-nature`, `font-modern`

## Development
Run `npm run dev` to start the development server with hot reloading for CSS changes.
