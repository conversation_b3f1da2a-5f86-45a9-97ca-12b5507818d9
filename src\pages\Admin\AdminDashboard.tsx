import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/Admin/AdminLayout';

interface StorySubmission {
  id: string;
  title: string;
  author: string;
  content: string;
  tags: string[];
  photo: File | null;
  photoUrl?: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
}

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [submissions, setSubmissions] = useState<StorySubmission[]>([]);

  useEffect(() => {
    // Load submissions from localStorage
    const storedSubmissions = JSON.parse(localStorage.getItem('storySubmissions') || '[]');
    setSubmissions(storedSubmissions.map((sub: any) => ({
      ...sub,
      submittedAt: new Date(sub.submittedAt)
    })));
  }, []);

  const handleSubmissionAction = (id: string, action: 'approved' | 'rejected') => {
    const updatedSubmissions = submissions.map(sub =>
      sub.id === id ? { ...sub, status: action } : sub
    );
    setSubmissions(updatedSubmissions);
    localStorage.setItem('storySubmissions', JSON.stringify(updatedSubmissions));
  };

  // Sample data for MVP dashboard
  const stats = [
    {
      name: 'Total Reservations',
      value: '127',
      change: '+12%',
      changeType: 'increase',
      icon: '📅'
    },
    {
      name: 'Occupancy Rate',
      value: '84%',
      change: '+5%',
      changeType: 'increase',
      icon: '🏕️'
    },
    {
      name: 'Revenue (This Month)',
      value: '$24,580',
      change: '+18%',
      changeType: 'increase',
      icon: '💰'
    },
    {
      name: 'Story Submissions',
      value: submissions.filter(s => s.status === 'pending').length.toString(),
      change: `${submissions.length} total`,
      changeType: 'increase',
      icon: '📝'
    }
  ];

  const recentReservations = [
    { id: 1, guest: 'Sarah Johnson', accommodation: 'Pine Tentalow', checkIn: '2024-12-28', status: 'Confirmed' },
    { id: 2, guest: 'Mike & Jennifer T.', accommodation: 'Cedar Bungalow', checkIn: '2024-12-30', status: 'Confirmed' },
    { id: 3, guest: 'David Chen', accommodation: 'Forest Cabin', checkIn: '2025-01-02', status: 'Pending' },
    { id: 4, guest: 'Emma Wilson', accommodation: 'Pine Tentalow', checkIn: '2025-01-05', status: 'Confirmed' },
    { id: 5, guest: 'Alex Rodriguez', accommodation: 'Cedar Bungalow', checkIn: '2025-01-08', status: 'Confirmed' }
  ];

  const upcomingEvents = [
    { id: 1, name: 'Forest Photography Workshop', date: '2024-12-28', participants: 12 },
    { id: 2, name: 'New Year Celebration', date: '2024-12-31', participants: 45 },
    { id: 3, name: 'Winter Hiking Adventure', date: '2025-01-03', participants: 8 },
    { id: 4, name: 'Stargazing Session', date: '2025-01-05', participants: 20 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'submissions', name: 'Story Submissions', icon: '📝', badge: submissions.filter(s => s.status === 'pending').length },
    { id: 'content', name: 'Content Management', icon: '🎨' },
    { id: 'analytics', name: 'Analytics', icon: '📈' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header with Tabs */}
        <div>
          <h1 className="text-3xl font-nature font-bold text-forest-green">Admin Dashboard</h1>
          <p className="text-stone-gray mt-2">Manage your Snawbra Campsite</p>

          {/* Tab Navigation */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-forest-green text-forest-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="flex items-center space-x-2">
                    <span>{tab.icon}</span>
                    <span>{tab.name}</span>
                    {tab.badge && tab.badge > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        {tab.badge}
                      </span>
                    )}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'dashboard' && (
          <>
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat) => (
                <div key={stat.name} className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-stone-gray">{stat.name}</p>
                      <p className="text-2xl font-bold text-forest-green">{stat.value}</p>
                    </div>
                    <div className="text-3xl">{stat.icon}</div>
                  </div>
                  <div className="mt-4">
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Reservations */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Recent Reservations</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentReservations.map((reservation) => (
                  <div key={reservation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{reservation.guest}</p>
                      <p className="text-sm text-stone-gray">{reservation.accommodation}</p>
                      <p className="text-sm text-stone-gray">Check-in: {reservation.checkIn}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(reservation.status)}`}>
                      {reservation.status}
                    </span>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">View All Reservations</button>
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Upcoming Events</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{event.name}</p>
                      <p className="text-sm text-stone-gray">{event.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-forest-green">{event.participants}</p>
                      <p className="text-xs text-stone-gray">participants</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">Manage Events</button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-forest-green mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <button className="flex flex-col items-center p-4 bg-forest-green text-white rounded-lg hover:bg-pine-green transition-colors">
              <span className="text-2xl mb-2">➕</span>
              <span className="text-sm">New Reservation</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-sage-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🎉</span>
              <span className="text-sm">Add Event</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-moss-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">📧</span>
              <span className="text-sm">Send Newsletter</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-earth-brown text-white rounded-lg hover:bg-bark-brown transition-colors">
              <span className="text-2xl mb-2">📊</span>
              <span className="text-sm">View Reports</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-pine-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🏕️</span>
              <span className="text-sm">Manage Rooms</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-stone-gray text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">⚙️</span>
              <span className="text-sm">Settings</span>
            </button>
          </div>
        </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-forest-green mb-6">System Status</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Booking System: Online</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Payment Gateway: Active</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Website: Operational</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Story Submissions Tab */}
        {activeTab === 'submissions' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">📝 Story Submissions</h2>
            {submissions.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <p className="text-gray-500 text-lg">No story submissions yet.</p>
                <p className="text-gray-400 text-sm mt-2">Submissions will appear here for review.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">{submission.title}</h3>
                        <p className="text-sm text-gray-500">By {submission.author} • {submission.submittedAt.toLocaleDateString()}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        submission.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        submission.status === 'approved' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <p className="text-gray-700 mb-4 leading-relaxed">{submission.content}</p>
                        <div className="flex flex-wrap gap-2">
                          {submission.tags.map((tag, index) => (
                            <span key={index} className="bg-forest-green bg-opacity-10 text-forest-green px-3 py-1 rounded-full text-sm font-medium">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {submission.photoUrl && (
                        <div>
                          <img
                            src={submission.photoUrl}
                            alt={submission.title}
                            className="w-full h-48 object-cover rounded-lg shadow-sm"
                          />
                        </div>
                      )}
                    </div>

                    {submission.status === 'pending' && (
                      <div className="flex space-x-4 mt-6 pt-4 border-t border-gray-200">
                        <button
                          onClick={() => handleSubmissionAction(submission.id, 'approved')}
                          className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors font-medium"
                        >
                          ✅ Approve & Publish
                        </button>
                        <button
                          onClick={() => handleSubmissionAction(submission.id, 'rejected')}
                          className="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors font-medium"
                        >
                          ❌ Reject
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Content Management Tab */}
        {activeTab === 'content' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">🎨 Content Management</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🎬</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Hero Section</h3>
                <p className="text-gray-600 mb-4">Manage hero section background videos and images</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Media
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🏠</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Accommodations</h3>
                <p className="text-gray-600 mb-4">Add, edit, or remove accommodation listings</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Listings
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Experiences</h3>
                <p className="text-gray-600 mb-4">Update available experiences and activities</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Experiences
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">📈 Analytics Dashboard</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-600 text-sm font-medium">Total Visits</p>
                    <p className="text-2xl font-bold text-blue-900">1,247</p>
                  </div>
                  <div className="text-blue-500 text-3xl">👥</div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-600 text-sm font-medium">Sign-up Clicks</p>
                    <p className="text-2xl font-bold text-green-900">89</p>
                  </div>
                  <div className="text-green-500 text-3xl">🌲</div>
                </div>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-600 text-sm font-medium">Experience Views</p>
                    <p className="text-2xl font-bold text-purple-900">156</p>
                  </div>
                  <div className="text-purple-500 text-3xl">🎯</div>
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-600 text-sm font-medium">Accommodation Views</p>
                    <p className="text-2xl font-bold text-orange-900">203</p>
                  </div>
                  <div className="text-orange-500 text-3xl">🏠</div>
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-600 text-sm font-medium">Event Views</p>
                    <p className="text-2xl font-bold text-red-900">78</p>
                  </div>
                  <div className="text-red-500 text-3xl">📅</div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-600 text-sm font-medium">Story Submissions</p>
                    <p className="text-2xl font-bold text-yellow-900">{submissions.length}</p>
                  </div>
                  <div className="text-yellow-500 text-3xl">📝</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
