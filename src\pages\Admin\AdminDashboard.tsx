import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/Admin/AdminLayout';

interface BlogSubmission {
  id: string;
  title: string;
  author: string;
  content: string;
  tags: string[];
  photo: File | null;
  photoUrl?: string;
  status: 'pending' | 'approved' | 'rejected' | 'featured';
  submittedAt: Date;
  votes?: number;
  socialShares?: number;
  adminNotes?: string;
  featuredMonth?: string;
}

interface DashboardStats {
  totalSubmissions: number;
  pendingReviews: number;
  featuredThisMonth: number;
  totalVotes: number;
  callAnalytics: {
    totalClicks: number;
    dailyClicks: number;
  };
}

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [submissions, setSubmissions] = useState<BlogSubmission[]>([]);
  const [eventProposals, setEventProposals] = useState<any[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    totalSubmissions: 0,
    pendingReviews: 0,
    featuredThisMonth: 0,
    totalVotes: 0,
    callAnalytics: { totalClicks: 0, dailyClicks: 0 }
  });
  const [selectedSubmission, setSelectedSubmission] = useState<BlogSubmission | null>(null);
  const [adminNotes, setAdminNotes] = useState('');

  useEffect(() => {
    // Load submissions from localStorage
    const storedSubmissions = JSON.parse(localStorage.getItem('blogSubmissions') || '[]');
    const loadedSubmissions = storedSubmissions.map((sub: any) => ({
      ...sub,
      submittedAt: new Date(sub.submittedAt)
    }));
    setSubmissions(loadedSubmissions);

    // Load event proposals from localStorage
    const storedProposals = JSON.parse(localStorage.getItem('eventProposals') || '[]');
    setEventProposals(storedProposals.map((proposal: any) => ({
      ...proposal,
      submittedAt: new Date(proposal.submittedAt)
    })));

    // Load call analytics
    const callAnalytics = JSON.parse(localStorage.getItem('callAnalytics') || '{"totalClicks": 0, "dailyClicks": 0}');

    // Calculate dashboard stats
    const currentMonth = new Date().toISOString().slice(0, 7);
    const stats: DashboardStats = {
      totalSubmissions: loadedSubmissions.length,
      pendingReviews: loadedSubmissions.filter(s => s.status === 'pending').length,
      featuredThisMonth: loadedSubmissions.filter(s => s.status === 'featured' && s.featuredMonth === currentMonth).length,
      totalVotes: loadedSubmissions.reduce((sum, s) => sum + (s.votes || 0), 0),
      callAnalytics
    };
    setDashboardStats(stats);
  }, []);

  const handleSubmissionAction = (id: string, action: 'approved' | 'rejected' | 'featured', notes?: string) => {
    const currentMonth = new Date().toISOString().slice(0, 7);
    const updatedSubmissions = submissions.map(sub =>
      sub.id === id ? {
        ...sub,
        status: action,
        adminNotes: notes || sub.adminNotes,
        featuredMonth: action === 'featured' ? currentMonth : sub.featuredMonth,
        votes: action === 'featured' ? (sub.votes || 0) : sub.votes
      } : sub
    );
    setSubmissions(updatedSubmissions);
    localStorage.setItem('blogSubmissions', JSON.stringify(updatedSubmissions));

    // Update dashboard stats
    const stats: DashboardStats = {
      totalSubmissions: updatedSubmissions.length,
      pendingReviews: updatedSubmissions.filter(s => s.status === 'pending').length,
      featuredThisMonth: updatedSubmissions.filter(s => s.status === 'featured' && s.featuredMonth === currentMonth).length,
      totalVotes: updatedSubmissions.reduce((sum, s) => sum + (s.votes || 0), 0),
      callAnalytics: dashboardStats.callAnalytics
    };
    setDashboardStats(stats);
    setSelectedSubmission(null);
  };

  const handleProposalAction = (id: string, action: 'approved' | 'rejected') => {
    const updatedProposals = eventProposals.map(proposal =>
      proposal.id === id ? { ...proposal, status: action } : proposal
    );
    setEventProposals(updatedProposals);
    localStorage.setItem('eventProposals', JSON.stringify(updatedProposals));
  };

  // Enhanced dashboard stats
  const stats = [
    {
      name: 'Blog Submissions',
      value: dashboardStats.totalSubmissions.toString(),
      change: `${dashboardStats.pendingReviews} pending`,
      changeType: 'neutral',
      icon: '📝'
    },
    {
      name: 'Featured This Month',
      value: dashboardStats.featuredThisMonth.toString(),
      change: 'out of 10 slots',
      changeType: dashboardStats.featuredThisMonth >= 10 ? 'complete' : 'increase',
      icon: '🏆'
    },
    {
      name: 'Total Votes',
      value: dashboardStats.totalVotes.toString(),
      change: 'community engagement',
      changeType: 'increase',
      icon: '🗳️'
    },
    {
      name: 'Call Analytics',
      value: dashboardStats.callAnalytics.totalClicks.toString(),
      change: `${dashboardStats.callAnalytics.dailyClicks} today`,
      changeType: 'increase',
      icon: '📞'
    }
  ];

  const recentReservations = [
    { id: 1, guest: 'Sarah Johnson', accommodation: 'Pine Tentalow', checkIn: '2024-12-28', status: 'Confirmed' },
    { id: 2, guest: 'Mike & Jennifer T.', accommodation: 'Cedar Bungalow', checkIn: '2024-12-30', status: 'Confirmed' },
    { id: 3, guest: 'David Chen', accommodation: 'Forest Cabin', checkIn: '2025-01-02', status: 'Pending' },
    { id: 4, guest: 'Emma Wilson', accommodation: 'Pine Tentalow', checkIn: '2025-01-05', status: 'Confirmed' },
    { id: 5, guest: 'Alex Rodriguez', accommodation: 'Cedar Bungalow', checkIn: '2025-01-08', status: 'Confirmed' }
  ];

  const upcomingEvents = [
    { id: 1, name: 'Forest Photography Workshop', date: '2024-12-28', participants: 12 },
    { id: 2, name: 'New Year Celebration', date: '2024-12-31', participants: 45 },
    { id: 3, name: 'Winter Hiking Adventure', date: '2025-01-03', participants: 8 },
    { id: 4, name: 'Stargazing Session', date: '2025-01-05', participants: 20 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'submissions', name: 'Blog Contest', icon: '🏆', badge: submissions.filter(s => s.status === 'pending').length },
    { id: 'events', name: 'Event Proposals', icon: '🎯', badge: eventProposals.filter(p => p.status === 'pending').length },
    { id: 'content', name: 'Content Management', icon: '🎨' },
    { id: 'analytics', name: 'Analytics', icon: '📈' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header with Tabs */}
        <div>
          <h1 className="text-3xl font-nature font-bold text-forest-green">Admin Dashboard</h1>
          <p className="text-stone-gray mt-2">Manage your Snawbra Campsite</p>

          {/* Tab Navigation */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-forest-green text-forest-green'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="flex items-center space-x-2">
                    <span>{tab.icon}</span>
                    <span>{tab.name}</span>
                    {tab.badge && tab.badge > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        {tab.badge}
                      </span>
                    )}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'dashboard' && (
          <>
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat) => (
                <div key={stat.name} className="bg-white rounded-lg shadow p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-stone-gray">{stat.name}</p>
                      <p className="text-2xl font-bold text-forest-green">{stat.value}</p>
                    </div>
                    <div className="text-3xl">{stat.icon}</div>
                  </div>
                  <div className="mt-4">
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Reservations */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Recent Reservations</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentReservations.map((reservation) => (
                  <div key={reservation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{reservation.guest}</p>
                      <p className="text-sm text-stone-gray">{reservation.accommodation}</p>
                      <p className="text-sm text-stone-gray">Check-in: {reservation.checkIn}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(reservation.status)}`}>
                      {reservation.status}
                    </span>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">View All Reservations</button>
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Upcoming Events</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{event.name}</p>
                      <p className="text-sm text-stone-gray">{event.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-forest-green">{event.participants}</p>
                      <p className="text-xs text-stone-gray">participants</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">Manage Events</button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-forest-green mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <button className="flex flex-col items-center p-4 bg-forest-green text-white rounded-lg hover:bg-pine-green transition-colors">
              <span className="text-2xl mb-2">➕</span>
              <span className="text-sm">New Reservation</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-sage-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🎉</span>
              <span className="text-sm">Add Event</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-moss-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">📧</span>
              <span className="text-sm">Send Newsletter</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-earth-brown text-white rounded-lg hover:bg-bark-brown transition-colors">
              <span className="text-2xl mb-2">📊</span>
              <span className="text-sm">View Reports</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-pine-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🏕️</span>
              <span className="text-sm">Manage Rooms</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-stone-gray text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">⚙️</span>
              <span className="text-sm">Settings</span>
            </button>
          </div>
        </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-forest-green mb-6">System Status</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Booking System: Online</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Payment Gateway: Active</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-stone-gray">Website: Operational</span>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Story Submissions Tab */}
        {activeTab === 'submissions' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-forest-green">🏆 Monthly Blog Contest</h2>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{dashboardStats.featuredThisMonth}/10</div>
                <div className="text-sm text-gray-500">Featured Posts</div>
              </div>
            </div>
            {submissions.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <p className="text-gray-500 text-lg">No story submissions yet.</p>
                <p className="text-gray-400 text-sm mt-2">Submissions will appear here for review.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {submissions.map((submission) => (
                  <div key={submission.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">{submission.title}</h3>
                        <p className="text-sm text-gray-500">By {submission.author} • {submission.submittedAt.toLocaleDateString()}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-bold ${
                        submission.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        submission.status === 'approved' ? 'bg-green-100 text-green-800' :
                        submission.status === 'featured' ? 'bg-orange-100 text-orange-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {submission.status === 'featured' ? '🏆 Featured' :
                         submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <p className="text-gray-700 mb-4 leading-relaxed">{submission.content}</p>
                        <div className="flex flex-wrap gap-2">
                          {submission.tags.map((tag, index) => (
                            <span key={index} className="bg-forest-green bg-opacity-10 text-forest-green px-3 py-1 rounded-full text-sm font-medium">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {submission.photoUrl && (
                        <div>
                          <img
                            src={submission.photoUrl}
                            alt={submission.title}
                            className="w-full h-48 object-cover rounded-lg shadow-sm"
                          />
                        </div>
                      )}
                    </div>

                    {submission.status === 'pending' && (
                      <div className="flex space-x-4 mt-6 pt-4 border-t border-gray-200 flex-wrap gap-2">
                        <button
                          onClick={() => setSelectedSubmission(submission)}
                          className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors font-medium"
                        >
                          📝 Review & Action
                        </button>
                        <button
                          onClick={() => handleSubmissionAction(submission.id, 'approved')}
                          className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors font-medium"
                        >
                          ✅ Quick Approve
                        </button>
                        <button
                          onClick={() => handleSubmissionAction(submission.id, 'rejected')}
                          className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors font-medium"
                        >
                          ❌ Quick Reject
                        </button>
                        {dashboardStats.featuredThisMonth < 10 && (
                          <button
                            onClick={() => handleSubmissionAction(submission.id, 'featured')}
                            className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 font-bold"
                          >
                            🏆 Feature This Month
                          </button>
                        )}
                      </div>
                    )}

                    {submission.status === 'approved' && dashboardStats.featuredThisMonth < 10 && (
                      <div className="flex space-x-4 mt-6 pt-4 border-t border-gray-200">
                        <button
                          onClick={() => handleSubmissionAction(submission.id, 'featured')}
                          className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 font-bold"
                        >
                          🏆 Feature This Month
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Review Modal */}
        {selectedSubmission && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-2xl font-bold text-gray-900">Review Submission</h3>
                    <button
                      onClick={() => setSelectedSubmission(null)}
                      className="text-gray-500 hover:text-gray-700 text-2xl"
                    >
                      ×
                    </button>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div>
                      <h4 className="font-bold text-lg">{selectedSubmission.title}</h4>
                      <p className="text-gray-600">by {selectedSubmission.author}</p>
                    </div>

                    <div>
                      <p className="text-gray-700">{selectedSubmission.content}</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Admin Notes (Optional)
                      </label>
                      <textarea
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green"
                        rows={3}
                        placeholder="Add notes about this submission..."
                      />
                    </div>
                  </div>

                  <div className="flex gap-3 flex-wrap">
                    <button
                      onClick={() => {
                        handleSubmissionAction(selectedSubmission.id, 'approved', adminNotes);
                        setAdminNotes('');
                      }}
                      className="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors font-bold"
                    >
                      ✅ Approve
                    </button>
                    <button
                      onClick={() => {
                        handleSubmissionAction(selectedSubmission.id, 'rejected', adminNotes);
                        setAdminNotes('');
                      }}
                      className="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors font-bold"
                    >
                      ❌ Reject
                    </button>
                    {dashboardStats.featuredThisMonth < 10 && (
                      <button
                        onClick={() => {
                          handleSubmissionAction(selectedSubmission.id, 'featured', adminNotes);
                          setAdminNotes('');
                        }}
                        className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 font-bold"
                      >
                        🏆 Feature This Month
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        )}

        {/* Event Proposals Tab */}
        {activeTab === 'events' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">🎯 Event Proposals</h2>
            {eventProposals.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🎯</div>
                <p className="text-gray-500 text-lg">No event proposals yet.</p>
                <p className="text-gray-400 text-sm mt-2">Event hosting proposals will appear here for review.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {eventProposals.map((proposal) => (
                  <div key={proposal.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">{proposal.title}</h3>
                        <p className="text-sm text-gray-500">By {proposal.contactInfo.name} • {proposal.submittedAt.toLocaleDateString()}</p>
                        <p className="text-sm text-gray-600 mt-1">📧 {proposal.contactInfo.email} • 📞 {proposal.contactInfo.phone}</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        proposal.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        proposal.status === 'approved' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <p className="text-gray-700 mb-4 leading-relaxed">{proposal.description}</p>

                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-600">Category:</span>
                            <span className="bg-forest-green bg-opacity-10 text-forest-green px-2 py-1 rounded text-xs font-medium">
                              {proposal.category}
                            </span>
                          </div>
                          {proposal.expectedAttendees && (
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-600">Expected Attendees:</span>
                              <span className="text-gray-700">{proposal.expectedAttendees}</span>
                            </div>
                          )}
                          {proposal.duration && (
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-600">Duration:</span>
                              <span className="text-gray-700">{proposal.duration}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {proposal.requirements && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900 mb-2">Special Requirements:</h4>
                          <p className="text-gray-700 text-sm">{proposal.requirements}</p>
                        </div>
                      )}
                    </div>

                    {proposal.status === 'pending' && (
                      <div className="flex space-x-4 mt-6 pt-4 border-t border-gray-200">
                        <button
                          onClick={() => handleProposalAction(proposal.id, 'approved')}
                          className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors font-medium"
                        >
                          ✅ Approve Event
                        </button>
                        <button
                          onClick={() => handleProposalAction(proposal.id, 'rejected')}
                          className="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors font-medium"
                        >
                          ❌ Reject
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Content Management Tab */}
        {activeTab === 'content' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">🎨 Content Management</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🎬</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Hero Section</h3>
                <p className="text-gray-600 mb-4">Manage hero section background videos and images</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Media
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🏠</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Accommodations</h3>
                <p className="text-gray-600 mb-4">Add, edit, or remove accommodation listings</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Listings
                </button>
              </div>

              <div className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Experiences</h3>
                <p className="text-gray-600 mb-4">Update available experiences and activities</p>
                <button className="w-full bg-forest-green text-white py-2 rounded-lg hover:bg-pine-green transition-colors">
                  Manage Experiences
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-2xl font-bold text-forest-green mb-6">📈 Analytics Dashboard</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-600 text-sm font-medium">Total Visits</p>
                    <p className="text-2xl font-bold text-blue-900">1,247</p>
                  </div>
                  <div className="text-blue-500 text-3xl">👥</div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-600 text-sm font-medium">Sign-up Clicks</p>
                    <p className="text-2xl font-bold text-green-900">89</p>
                  </div>
                  <div className="text-green-500 text-3xl">🌲</div>
                </div>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-600 text-sm font-medium">Experience Views</p>
                    <p className="text-2xl font-bold text-purple-900">156</p>
                  </div>
                  <div className="text-purple-500 text-3xl">🎯</div>
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-600 text-sm font-medium">Accommodation Views</p>
                    <p className="text-2xl font-bold text-orange-900">203</p>
                  </div>
                  <div className="text-orange-500 text-3xl">🏠</div>
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-600 text-sm font-medium">Event Views</p>
                    <p className="text-2xl font-bold text-red-900">78</p>
                  </div>
                  <div className="text-red-500 text-3xl">📅</div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-600 text-sm font-medium">Story Submissions</p>
                    <p className="text-2xl font-bold text-yellow-900">{submissions.length}</p>
                  </div>
                  <div className="text-yellow-500 text-3xl">📝</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
