import React, { useState } from 'react';

interface EventProposal {
  id: string;
  title: string;
  description: string;
  category: string;
  expectedAttendees: string;
  duration: string;
  requirements: string;
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  submittedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
}

interface EventHostingProps {
  onClose: () => void;
}

const EventHosting: React.FC<EventHostingProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    expectedAttendees: '',
    duration: '',
    requirements: '',
    contactInfo: {
      name: '',
      email: '',
      phone: ''
    }
  });

  const eventCategories = [
    { id: 'wellness', name: 'Wellness & Mindfulness', icon: '🧘', description: 'Yoga, meditation, healing workshops' },
    { id: 'workshop', name: 'Skills & Crafts', icon: '🔨', description: 'DIY workshops, art classes, skill sharing' },
    { id: 'music', name: 'Music & Performance', icon: '🎵', description: 'Concerts, open mic nights, performances' },
    { id: 'food', name: 'Food & Cooking', icon: '🍳', description: 'Cooking classes, food festivals, tastings' },
    { id: 'nature', name: 'Nature & Adventure', icon: '🌲', description: 'Hiking groups, nature walks, outdoor activities' },
    { id: 'family', name: 'Family & Kids', icon: '👨‍👩‍👧‍👦', description: 'Family activities, kids workshops, games' },
    { id: 'education', name: 'Learning & Education', icon: '📚', description: 'Lectures, seminars, educational workshops' },
    { id: 'celebration', name: 'Celebrations & Festivals', icon: '🎉', description: 'Seasonal celebrations, cultural festivals' }
  ];

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = () => {
    const proposal: EventProposal = {
      id: Date.now().toString(),
      ...formData,
      submittedAt: new Date(),
      status: 'pending'
    };

    // Store in localStorage for admin review
    const existingProposals = JSON.parse(localStorage.getItem('eventProposals') || '[]');
    existingProposals.push(proposal);
    localStorage.setItem('eventProposals', JSON.stringify(existingProposals));

    alert('🎉 Your event proposal has been submitted! We\'ll review it and get back to you within 48 hours.');
    onClose();
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-forest-green mb-4">🌟 Choose Your Event Type</h2>
        <p className="text-gray-600">What kind of amazing experience do you want to create?</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {eventCategories.map((category) => (
          <button
            key={category.id}
            onClick={() => {
              handleInputChange('category', category.id);
              setCurrentStep(2);
            }}
            className={`p-6 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              formData.category === category.id
                ? 'border-forest-green bg-forest-green bg-opacity-10'
                : 'border-gray-200 hover:border-forest-green'
            }`}
          >
            <div className="text-4xl mb-3">{category.icon}</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{category.name}</h3>
            <p className="text-sm text-gray-600">{category.description}</p>
          </button>
        ))}
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-forest-green mb-4">📝 Tell Us About Your Event</h2>
        <p className="text-gray-600">Share the details that will make your event special</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Event Title *</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Give your event an exciting name..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Event Description *</label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe what participants can expect, what they'll learn or experience..."
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Expected Attendees</label>
          <select
            value={formData.expectedAttendees}
            onChange={(e) => handleInputChange('expectedAttendees', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          >
            <option value="">Select group size...</option>
            <option value="1-10">Small Group (1-10 people)</option>
            <option value="11-25">Medium Group (11-25 people)</option>
            <option value="26-50">Large Group (26-50 people)</option>
            <option value="50+">Community Event (50+ people)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Duration</label>
          <select
            value={formData.duration}
            onChange={(e) => handleInputChange('duration', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          >
            <option value="">Select duration...</option>
            <option value="1-2 hours">1-2 hours</option>
            <option value="Half day">Half day (3-4 hours)</option>
            <option value="Full day">Full day (6-8 hours)</option>
            <option value="Weekend">Weekend event</option>
            <option value="Multi-day">Multi-day event</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Special Requirements</label>
          <textarea
            value={formData.requirements}
            onChange={(e) => handleInputChange('requirements', e.target.value)}
            placeholder="Any special equipment, setup, or facilities needed..."
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep(1)}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        <button
          onClick={() => setCurrentStep(3)}
          disabled={!formData.title || !formData.description}
          className="px-6 py-3 bg-forest-green text-white rounded-lg hover:bg-pine-green transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continue →
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-forest-green mb-4">📞 Contact Information</h2>
        <p className="text-gray-600">How can we reach you to discuss your event?</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
          <input
            type="text"
            value={formData.contactInfo.name}
            onChange={(e) => handleInputChange('contactInfo.name', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
          <input
            type="email"
            value={formData.contactInfo.email}
            onChange={(e) => handleInputChange('contactInfo.email', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
          <input
            type="tel"
            value={formData.contactInfo.phone}
            onChange={(e) => handleInputChange('contactInfo.phone', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-forest-green focus:border-transparent"
          />
        </div>
      </div>

      <div className="bg-forest-green bg-opacity-10 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-forest-green mb-3">🎯 What Happens Next?</h3>
        <ul className="space-y-2 text-gray-700">
          <li className="flex items-center gap-2">
            <span className="text-green-500">✓</span>
            We'll review your proposal within 48 hours
          </li>
          <li className="flex items-center gap-2">
            <span className="text-green-500">✓</span>
            Our team will contact you to discuss details
          </li>
          <li className="flex items-center gap-2">
            <span className="text-green-500">✓</span>
            We'll help you plan and promote your event
          </li>
          <li className="flex items-center gap-2">
            <span className="text-green-500">✓</span>
            Enjoy creating amazing memories with our community!
          </li>
        </ul>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep(2)}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          ← Back
        </button>
        <button
          onClick={handleSubmit}
          disabled={!formData.contactInfo.name || !formData.contactInfo.email}
          className="px-8 py-3 bg-gradient-to-r from-forest-green to-pine-green text-white rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed font-semibold"
        >
          🚀 Submit Proposal
        </button>
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-forest-green">Host Your Event</h1>
            <div className="flex gap-2">
              {[1, 2, 3].map((step) => (
                <div
                  key={step}
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step <= currentStep
                      ? 'bg-forest-green text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}
                >
                  {step}
                </div>
              ))}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="p-6">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>
      </div>
    </div>
  );
};

export default EventHosting;
