import React, { useState, useEffect, useRef } from 'react';
import { useAppContext } from '../../context/AppContext';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const ChatWindow: React.FC = () => {
  const {
    updateUserPreferences,
    userPreferences
  } = useAppContext();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: 'welcome',
      text: "Wonderful! Now, what kind of experience are you dreaming of? A quiet retreat for two, a fun family adventure, or a solo hiking trip?",
      isUser: false,
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const generateAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Detect trip type preferences
    if (lowerMessage.includes('family') || lowerMessage.includes('kids') || lowerMessage.includes('children')) {
      updateUserPreferences({ tripType: 'family', interests: ['family-friendly'] });
      return "Great choice! Our Cedar Bungalows are perfect for families, and the guided nature trail is a hit with kids. Would you like me to show you the accommodations?";
    }
    
    if (lowerMessage.includes('couple') || lowerMessage.includes('romantic') || lowerMessage.includes('two')) {
      updateUserPreferences({ tripType: 'couple', interests: ['romantic'] });
      return "How romantic! Our Pine Tentalows offer the perfect intimate setting with beautiful forest views. They're ideal for couples seeking a peaceful retreat.";
    }
    
    if (lowerMessage.includes('solo') || lowerMessage.includes('alone') || lowerMessage.includes('myself')) {
      updateUserPreferences({ tripType: 'solo', interests: ['solo-travel'] });
      return "Solo adventures are so rewarding! I'd recommend our cozy single accommodations and our self-guided trail maps. Perfect for some peaceful reflection in nature.";
    }
    
    if (lowerMessage.includes('hiking') || lowerMessage.includes('trail') || lowerMessage.includes('walk')) {
      updateUserPreferences({ interests: [...(userPreferences.interests || []), 'hiking'] });
      return "Excellent! We have several hiking trails ranging from easy nature walks to challenging mountain paths. Our guided horseback riding trail is also very popular!";
    }
    
    if (lowerMessage.includes('accommodation') || lowerMessage.includes('stay') || lowerMessage.includes('room')) {
      return "We offer several accommodation types: Cedar Bungalows (perfect for families), Pine Tentalows (glamping experience), and traditional camping spots. What sounds most appealing to you?";
    }
    
    if (lowerMessage.includes('activity') || lowerMessage.includes('experience') || lowerMessage.includes('do')) {
      return "We have amazing experiences! Guided nature trails, horseback riding, wildlife watching, campfire storytelling, and seasonal workshops. What type of activities interest you most?";
    }
    
    if (lowerMessage.includes('event') || lowerMessage.includes('calendar') || lowerMessage.includes('schedule')) {
      return "We host regular community events! Check our events calendar for upcoming workshops, seasonal festivals, and group activities. Would you like me to highlight some upcoming events?";
    }
    
    // Default responses
    const defaultResponses = [
      "That sounds wonderful! Tell me more about what you're looking for in your nature getaway.",
      "I'd love to help you plan the perfect experience. What aspects of camping and nature appeal to you most?",
      "Great! Based on what you're telling me, I can suggest some perfect options for your stay.",
      "That's exactly the kind of experience we specialize in! Let me know if you'd like specific recommendations."
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: `user_${Date.now()}`,
      text: inputValue,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse: Message = {
        id: `ai_${Date.now()}`,
        text: generateAIResponse(inputValue),
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
      
      // Log to console for debugging
      console.log('User preferences updated:', userPreferences);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="fixed bottom-24 right-6 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col z-40">
      {/* Header */}
      <div className="bg-forest-green text-white p-4 rounded-t-lg">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-sage-green rounded-full flex items-center justify-center mr-3">
            <span className="text-sm">🤖</span>
          </div>
          <div>
            <h3 className="font-semibold">AI Concierge</h3>
            <p className="text-xs text-sage-green">Here to help plan your stay</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                message.isUser
                  ? 'bg-forest-green text-white'
                  : 'bg-gray-100 text-stone-gray'
              }`}
            >
              {message.text}
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-stone-gray px-3 py-2 rounded-lg text-sm">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Tell me about your ideal getaway..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent text-sm"
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            className="bg-forest-green text-white px-3 py-2 rounded-lg hover:bg-pine-green transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatWindow;
