/* Enhanced Calendar Styling */
.enhanced-calendar {
  font-family: 'Inter', sans-serif;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Calendar Header */
.enhanced-calendar .rbc-header {
  background: linear-gradient(135deg, #228B22, #2F4F2F);
  color: white;
  font-weight: 600;
  padding: 15px 10px;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
}

/* Calendar Toolbar */
.enhanced-calendar .rbc-toolbar {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 2px solid #e9ecef;
  margin-bottom: 0;
}

.enhanced-calendar .rbc-toolbar button {
  background: white;
  border: 2px solid #228B22;
  color: #228B22;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 0 4px;
}

.enhanced-calendar .rbc-toolbar button:hover {
  background: #228B22;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(34, 139, 34, 0.3);
}

.enhanced-calendar .rbc-toolbar button.rbc-active {
  background: #228B22;
  color: white;
  box-shadow: 0 4px 12px rgba(34, 139, 34, 0.3);
}

/* Calendar Month View */
.enhanced-calendar .rbc-month-view {
  border: none;
}

.enhanced-calendar .rbc-date-cell {
  padding: 8px;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  min-height: 120px;
  background: white;
  transition: background-color 0.2s ease;
}

.enhanced-calendar .rbc-date-cell:hover {
  background: #f8f9fa;
}

.enhanced-calendar .rbc-off-range-bg {
  background: #f8f9fa;
}

.enhanced-calendar .rbc-today {
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.1), rgba(47, 79, 47, 0.05));
  border: 2px solid #228B22;
  border-radius: 8px;
}

/* Calendar Events */
.enhanced-calendar .rbc-event {
  border-radius: 6px;
  border: none;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  margin: 2px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
}

.enhanced-calendar .rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.enhanced-calendar .rbc-event-label {
  font-size: 11px;
  font-weight: 500;
  opacity: 0.8;
}

/* Week and Day Views */
.enhanced-calendar .rbc-time-view {
  border: none;
}

.enhanced-calendar .rbc-time-header {
  border-bottom: 2px solid #e9ecef;
}

.enhanced-calendar .rbc-time-content {
  border-top: none;
}

.enhanced-calendar .rbc-timeslot-group {
  border-bottom: 1px solid #f1f3f4;
}

.enhanced-calendar .rbc-time-slot {
  border-top: 1px solid #f8f9fa;
}

.enhanced-calendar .rbc-current-time-indicator {
  background-color: #228B22;
  height: 3px;
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(34, 139, 34, 0.3);
}

/* Calendar Navigation */
.enhanced-calendar .rbc-btn-group button {
  background: white;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 6px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.enhanced-calendar .rbc-btn-group button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.enhanced-calendar .rbc-btn-group button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.enhanced-calendar .rbc-btn-group button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Popup Styling */
.enhanced-calendar .rbc-overlay {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 16px;
}

.enhanced-calendar .rbc-overlay-header {
  background: #228B22;
  color: white;
  padding: 12px 16px;
  margin: -16px -16px 16px -16px;
  border-radius: 12px 12px 0 0;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-calendar .rbc-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .enhanced-calendar .rbc-toolbar-label {
    order: -1;
    font-size: 18px;
    font-weight: 700;
    color: #228B22;
  }
  
  .enhanced-calendar .rbc-btn-group {
    display: flex;
    justify-content: center;
  }
  
  .enhanced-calendar .rbc-date-cell {
    min-height: 80px;
  }
  
  .enhanced-calendar .rbc-event {
    font-size: 11px;
    padding: 2px 6px;
  }
}

/* Animation for fade-in */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out;
}

/* Calendar Container */
.calendar-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.calendar-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #228B22, #2F4F2F, #228B22);
  z-index: 1;
}
