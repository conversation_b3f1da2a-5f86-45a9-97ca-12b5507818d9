import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

// Types
export interface UserPreferences {
  userId: string;
  tripType?: string;
  interests?: string[];
  accommodationType?: string;
  groupSize?: number;
  budget?: string;
  activities?: string[];
}

export interface SiteContext {
  accommodations: any[];
  experiences: any[];
  events: any[];
  communityStories: any[];
}

export interface User {
  name: string;
  email: string;
  role: 'admin' | 'user';
  avatar?: string;
}

interface AppContextType {
  // User state
  isLoggedIn: boolean;
  userPreferences: UserPreferences;
  setIsLoggedIn: (value: boolean) => void;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;

  // Authentication state
  isAuthenticated: boolean;
  user: User | null;
  setIsAuthenticated: (value: boolean) => void;
  setUser: (user: User | null) => void;

  // Modal state
  showWelcomeModal: boolean;
  setShowWelcomeModal: (value: boolean) => void;
  
  // Chat state
  isChatOpen: boolean;
  setIsChatOpen: (value: boolean) => void;
  chatMessages: any[];
  addChatMessage: (message: any) => void;
  
  // Loading state
  isLoading: boolean;
  setIsLoading: (value: boolean) => void;
  
  // Site context for AI
  siteContext: SiteContext;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // User state
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    userId: 'demo_user_123'
  });

  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  
  // Modal state
  const [showWelcomeModal, setShowWelcomeModal] = useState(false);
  
  // Chat state
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  
  // Loading state
  const [isLoading, setIsLoading] = useState(true);
  
  // Site context for AI
  const [siteContext] = useState<SiteContext>({
    accommodations: [
      {
        id: 1,
        name: 'Cedar Bungalow',
        type: 'bungalow',
        capacity: 4,
        amenities: ['fireplace', 'kitchen', 'wifi'],
        description: 'Cozy cedar bungalow perfect for families'
      },
      {
        id: 2,
        name: 'Pine Tentalow',
        type: 'tentalow',
        capacity: 2,
        amenities: ['heating', 'electricity'],
        description: 'Glamping experience with modern amenities'
      }
    ],
    experiences: [
      {
        id: 1,
        name: 'Guided Nature Trail',
        duration: '2 hours',
        difficulty: 'easy',
        description: 'Family-friendly nature walk with expert guide'
      },
      {
        id: 2,
        name: 'Horseback Riding',
        duration: '3 hours',
        difficulty: 'moderate',
        description: 'Scenic horseback ride through forest trails'
      }
    ],
    events: [],
    communityStories: []
  });

  // Check for first visit and restore authentication
  useEffect(() => {
    const hasVisited = localStorage.getItem('snawbra_visited');
    if (!hasVisited && !isLoggedIn) {
      setShowWelcomeModal(true);
    }

    // Restore demo user session
    const savedUser = localStorage.getItem('demoUser');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error restoring user session:', error);
        localStorage.removeItem('demoUser');
      }
    }

    // Simulate loading
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }, [isLoggedIn]);

  const updateUserPreferences = (preferences: Partial<UserPreferences>) => {
    setUserPreferences(prev => {
      const updated = { ...prev, ...preferences };
      console.log('User preferences updated:', updated);
      return updated;
    });
  };

  const addChatMessage = (message: any) => {
    setChatMessages(prev => [...prev, message]);
  };

  const value: AppContextType = {
    isLoggedIn,
    userPreferences,
    setIsLoggedIn,
    updateUserPreferences,
    isAuthenticated,
    user,
    setIsAuthenticated,
    setUser,
    showWelcomeModal,
    setShowWelcomeModal,
    isChatOpen,
    setIsChatOpen,
    chatMessages,
    addChatMessage,
    isLoading,
    setIsLoading,
    siteContext
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
