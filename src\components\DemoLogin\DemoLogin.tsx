import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';

interface DemoLoginProps {
  onClose: () => void;
}

const DemoLogin: React.FC<DemoLoginProps> = ({ onClose }) => {
  const { setIsAuthenticated, setUser } = useAppContext();
  const [selectedRole, setSelectedRole] = useState<'admin' | 'user' | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const demoAccounts = [
    {
      role: 'admin' as const,
      name: 'Admin Demo',
      email: '<EMAIL>',
      avatar: '👨‍💼',
      description: 'Full access to admin dashboard, content management, and all features',
      features: ['Admin Dashboard', 'Content Management', 'User Management', 'Analytics', 'Event Management']
    },
    {
      role: 'user' as const,
      name: 'Guest User',
      email: '<EMAIL>',
      avatar: '🏕️',
      description: 'Standard user experience with booking and community features',
      features: ['Book Accommodations', 'Join Events', 'Share Stories', 'Community Access', 'Profile Management']
    }
  ];

  const handleLogin = async (role: 'admin' | 'user') => {
    setIsLoading(true);
    
    // Simulate login delay for realistic experience
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const account = demoAccounts.find(acc => acc.role === role);
    if (account) {
      setUser({
        name: account.name,
        email: account.email,
        role: account.role,
        avatar: account.avatar
      });
      setIsAuthenticated(true);
      
      // Store in localStorage for persistence
      localStorage.setItem('demoUser', JSON.stringify({
        name: account.name,
        email: account.email,
        role: account.role,
        avatar: account.avatar
      }));
      
      onClose();
      
      // Redirect to admin dashboard if admin login
      if (role === 'admin') {
        window.location.href = '/admin';
      }
    }
    
    setIsLoading(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div className="sticky top-0 bg-gradient-to-r from-forest-green to-pine-green text-white p-6 rounded-t-2xl">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold">🚀 Demo Login</h1>
              <p className="text-green-100 mt-1">Experience Snawbra Campsite with instant access</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl transition-colors"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Demo Experience</h2>
            <p className="text-gray-600">Select a role to explore different features and capabilities</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {demoAccounts.map((account) => (
              <div
                key={account.role}
                className={`border-2 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                  selectedRole === account.role
                    ? 'border-forest-green bg-forest-green bg-opacity-5 shadow-lg'
                    : 'border-gray-200 hover:border-forest-green'
                }`}
                onClick={() => setSelectedRole(account.role)}
              >
                <div className="text-center mb-4">
                  <div className="text-6xl mb-3">{account.avatar}</div>
                  <h3 className="text-xl font-bold text-gray-900">{account.name}</h3>
                  <p className="text-sm text-gray-500">{account.email}</p>
                </div>
                
                <p className="text-gray-700 text-sm mb-4 text-center">{account.description}</p>
                
                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-900 text-sm">Features Available:</h4>
                  <ul className="space-y-1">
                    {account.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2 text-sm text-gray-600">
                        <span className="text-green-500">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          {selectedRole && (
            <div className="bg-gradient-to-r from-forest-green to-pine-green rounded-xl p-6 text-white text-center">
              <h3 className="text-xl font-bold mb-2">
                Ready to explore as {demoAccounts.find(acc => acc.role === selectedRole)?.name}?
              </h3>
              <p className="text-green-100 mb-4">
                {selectedRole === 'admin' 
                  ? 'You\'ll have full access to the admin dashboard and management features.'
                  : 'You\'ll experience the site as a regular user with booking and community access.'
                }
              </p>
              <button
                onClick={() => handleLogin(selectedRole)}
                disabled={isLoading}
                className="bg-white text-forest-green px-8 py-3 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-forest-green"></div>
                    Logging in...
                  </div>
                ) : (
                  `🎯 Login as ${selectedRole === 'admin' ? 'Admin' : 'User'}`
                )}
              </button>
            </div>
          )}

          <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start gap-3">
              <div className="text-blue-500 text-xl">💡</div>
              <div>
                <h4 className="font-semibold text-blue-900 mb-1">Demo Information</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• No real registration required - instant access</li>
                  <li>• All data is stored locally for demonstration</li>
                  <li>• Admin access includes full dashboard functionality</li>
                  <li>• User access includes booking and community features</li>
                  <li>• You can switch between roles anytime</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoLogin;
