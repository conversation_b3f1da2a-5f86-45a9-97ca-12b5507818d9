import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';

interface DemoLoginProps {
  onClose: () => void;
}

const DemoLogin: React.FC<DemoLoginProps> = ({ onClose }) => {
  const { setIsAuthenticated, setUser } = useAppContext();
  const [isLoading, setIsLoading] = useState(false);

  const handleQuickLogin = async (role: 'admin' | 'user') => {
    setIsLoading(true);

    const userData = role === 'admin'
      ? { name: 'Admin Demo', email: '<EMAIL>', role: 'admin' as const, avatar: '👨‍💼' }
      : { name: 'Guest User', email: '<EMAIL>', role: 'user' as const, avatar: '🏕️' };

    // Quick login without delay
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('demoUser', JSON.stringify(userData));

    onClose();

    if (role === 'admin') {
      window.location.href = '/admin';
    }

    setIsLoading(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full shadow-2xl">
        <div className="bg-gradient-to-r from-forest-green to-pine-green text-white p-6 rounded-t-2xl">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-xl font-bold">🚀 Quick Demo Access</h1>
              <p className="text-green-100 text-sm mt-1">Choose your role</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-2xl transition-colors"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          <button
            onClick={() => handleQuickLogin('admin')}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50"
          >
            <div className="flex items-center justify-center gap-3">
              <span className="text-2xl">👨‍💼</span>
              <div className="text-left">
                <div>Admin Access</div>
                <div className="text-sm opacity-90">Full dashboard control</div>
              </div>
            </div>
          </button>

          <button
            onClick={() => handleQuickLogin('user')}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-4 px-6 rounded-xl font-bold text-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50"
          >
            <div className="flex items-center justify-center gap-3">
              <span className="text-2xl">🏕️</span>
              <div className="text-left">
                <div>User Access</div>
                <div className="text-sm opacity-90">Booking & community</div>
              </div>
            </div>
          </button>

          {isLoading && (
            <div className="text-center py-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-forest-green mx-auto"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DemoLogin;
